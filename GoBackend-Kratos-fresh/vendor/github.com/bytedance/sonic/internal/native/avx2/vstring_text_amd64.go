// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package avx2

var _text_vstring = []byte{
	// .p2align 5, 0x00
	// LCPI0_0
	0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, // QUAD $0x2222222222222222; QUAD $0x2222222222222222  // .space 16, '""""""""""""""""'
	0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, //0x00000010 QUAD $0x2222222222222222; QUAD $0x2222222222222222  // .space 16, '""""""""""""""""'
	//0x00000020 LCPI0_1
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x00000020 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x00000030 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	//0x00000040 LCPI0_2
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, //0x00000040 QUAD $0x2020202020202020; QUAD $0x2020202020202020  // .space 16, '                '
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, //0x00000050 QUAD $0x2020202020202020; QUAD $0x2020202020202020  // .space 16, '                '
	//0x00000060 .p2align 4, 0x90
	//0x00000060 _vstring
	0x55, //0x00000060 pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000061 movq         %rsp, %rbp
	0x41, 0x57, //0x00000064 pushq        %r15
	0x41, 0x56, //0x00000066 pushq        %r14
	0x41, 0x55, //0x00000068 pushq        %r13
	0x41, 0x54, //0x0000006a pushq        %r12
	0x53, //0x0000006c pushq        %rbx
	0x48, 0x83, 0xec, 0x18, //0x0000006d subq         $24, %rsp
	0x4c, 0x8b, 0x16, //0x00000071 movq         (%rsi), %r10
	0xf6, 0xc1, 0x20, //0x00000074 testb        $32, %cl
	0x0f, 0x85, 0x2b, 0x01, 0x00, 0x00, //0x00000077 jne          LBB0_12
	0x4c, 0x8b, 0x6f, 0x08, //0x0000007d movq         $8(%rdi), %r13
	0x4c, 0x89, 0x6d, 0xc8, //0x00000081 movq         %r13, $-56(%rbp)
	0x4d, 0x29, 0xd5, //0x00000085 subq         %r10, %r13
	0x0f, 0x84, 0x9e, 0x03, 0x00, 0x00, //0x00000088 je           LBB0_41
	0x4c, 0x8b, 0x1f, //0x0000008e movq         (%rdi), %r11
	0x49, 0x83, 0xfd, 0x40, //0x00000091 cmpq         $64, %r13
	0x0f, 0x82, 0x9d, 0x03, 0x00, 0x00, //0x00000095 jb           LBB0_42
	0x4c, 0x89, 0xd3, //0x0000009b movq         %r10, %rbx
	0x48, 0xf7, 0xd3, //0x0000009e notq         %rbx
	0x48, 0xc7, 0x45, 0xd0, 0xff, 0xff, 0xff, 0xff, //0x000000a1 movq         $-1, $-48(%rbp)
	0x45, 0x31, 0xe4, //0x000000a9 xorl         %r12d, %r12d
	0xc5, 0xfe, 0x6f, 0x05, 0x4c, 0xff, 0xff, 0xff, //0x000000ac vmovdqu      $-180(%rip), %ymm0  /* LCPI0_0+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x0d, 0x64, 0xff, 0xff, 0xff, //0x000000b4 vmovdqu      $-156(%rip), %ymm1  /* LCPI0_1+0(%rip) */
	0x49, 0xbf, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000000bc movabsq      $6148914691236517205, %r15
	0x4d, 0x89, 0xd0, //0x000000c6 movq         %r10, %r8
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000000c9 .p2align 4, 0x90
	//0x000000d0 LBB0_4
	0xc4, 0x81, 0x7e, 0x6f, 0x14, 0x03, //0x000000d0 vmovdqu      (%r11,%r8), %ymm2
	0xc4, 0x81, 0x7e, 0x6f, 0x5c, 0x03, 0x20, //0x000000d6 vmovdqu      $32(%r11,%r8), %ymm3
	0xc5, 0xed, 0x74, 0xe0, //0x000000dd vpcmpeqb     %ymm0, %ymm2, %ymm4
	0xc5, 0x7d, 0xd7, 0xcc, //0x000000e1 vpmovmskb    %ymm4, %r9d
	0xc5, 0xe5, 0x74, 0xe0, //0x000000e5 vpcmpeqb     %ymm0, %ymm3, %ymm4
	0xc5, 0xfd, 0xd7, 0xcc, //0x000000e9 vpmovmskb    %ymm4, %ecx
	0xc5, 0xed, 0x74, 0xd1, //0x000000ed vpcmpeqb     %ymm1, %ymm2, %ymm2
	0xc5, 0xfd, 0xd7, 0xc2, //0x000000f1 vpmovmskb    %ymm2, %eax
	0xc5, 0xe5, 0x74, 0xd1, //0x000000f5 vpcmpeqb     %ymm1, %ymm3, %ymm2
	0xc5, 0xfd, 0xd7, 0xfa, //0x000000f9 vpmovmskb    %ymm2, %edi
	0x48, 0xc1, 0xe1, 0x20, //0x000000fd shlq         $32, %rcx
	0x49, 0x09, 0xc9, //0x00000101 orq          %rcx, %r9
	0x48, 0xc1, 0xe7, 0x20, //0x00000104 shlq         $32, %rdi
	0x48, 0x09, 0xf8, //0x00000108 orq          %rdi, %rax
	0x0f, 0x85, 0x30, 0x00, 0x00, 0x00, //0x0000010b jne          LBB0_8
	0x4d, 0x85, 0xe4, //0x00000111 testq        %r12, %r12
	0x0f, 0x85, 0x3d, 0x00, 0x00, 0x00, //0x00000114 jne          LBB0_10
	0x45, 0x31, 0xe4, //0x0000011a xorl         %r12d, %r12d
	0x4d, 0x85, 0xc9, //0x0000011d testq        %r9, %r9
	0x0f, 0x85, 0x79, 0x00, 0x00, 0x00, //0x00000120 jne          LBB0_11
	//0x00000126 LBB0_7
	0x49, 0x83, 0xc5, 0xc0, //0x00000126 addq         $-64, %r13
	0x48, 0x83, 0xc3, 0xc0, //0x0000012a addq         $-64, %rbx
	0x49, 0x83, 0xc0, 0x40, //0x0000012e addq         $64, %r8
	0x49, 0x83, 0xfd, 0x3f, //0x00000132 cmpq         $63, %r13
	0x0f, 0x87, 0x94, 0xff, 0xff, 0xff, //0x00000136 ja           LBB0_4
	0xe9, 0x28, 0x02, 0x00, 0x00, //0x0000013c jmp          LBB0_30
	//0x00000141 LBB0_8
	0x48, 0x83, 0x7d, 0xd0, 0xff, //0x00000141 cmpq         $-1, $-48(%rbp)
	0x0f, 0x85, 0x0b, 0x00, 0x00, 0x00, //0x00000146 jne          LBB0_10
	0x48, 0x0f, 0xbc, 0xc8, //0x0000014c bsfq         %rax, %rcx
	0x4c, 0x01, 0xc1, //0x00000150 addq         %r8, %rcx
	0x48, 0x89, 0x4d, 0xd0, //0x00000153 movq         %rcx, $-48(%rbp)
	//0x00000157 LBB0_10
	0x4c, 0x89, 0xe1, //0x00000157 movq         %r12, %rcx
	0x48, 0xf7, 0xd1, //0x0000015a notq         %rcx
	0x48, 0x21, 0xc1, //0x0000015d andq         %rax, %rcx
	0x4c, 0x8d, 0x34, 0x09, //0x00000160 leaq         (%rcx,%rcx), %r14
	0x4d, 0x09, 0xe6, //0x00000164 orq          %r12, %r14
	0x4c, 0x89, 0xf7, //0x00000167 movq         %r14, %rdi
	0x48, 0xf7, 0xd7, //0x0000016a notq         %rdi
	0x48, 0x21, 0xc7, //0x0000016d andq         %rax, %rdi
	0x48, 0xb8, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000170 movabsq      $-6148914691236517206, %rax
	0x48, 0x21, 0xc7, //0x0000017a andq         %rax, %rdi
	0x45, 0x31, 0xe4, //0x0000017d xorl         %r12d, %r12d
	0x48, 0x01, 0xcf, //0x00000180 addq         %rcx, %rdi
	0x41, 0x0f, 0x92, 0xc4, //0x00000183 setb         %r12b
	0x48, 0x01, 0xff, //0x00000187 addq         %rdi, %rdi
	0x4c, 0x31, 0xff, //0x0000018a xorq         %r15, %rdi
	0x4c, 0x21, 0xf7, //0x0000018d andq         %r14, %rdi
	0x48, 0xf7, 0xd7, //0x00000190 notq         %rdi
	0x49, 0x21, 0xf9, //0x00000193 andq         %rdi, %r9
	0x4d, 0x85, 0xc9, //0x00000196 testq        %r9, %r9
	0x0f, 0x84, 0x87, 0xff, 0xff, 0xff, //0x00000199 je           LBB0_7
	//0x0000019f LBB0_11
	0x4d, 0x0f, 0xbc, 0xf1, //0x0000019f bsfq         %r9, %r14
	0xe9, 0x8c, 0x01, 0x00, 0x00, //0x000001a3 jmp          LBB0_27
	//0x000001a8 LBB0_12
	0x4c, 0x8b, 0x6f, 0x08, //0x000001a8 movq         $8(%rdi), %r13
	0x4c, 0x89, 0x6d, 0xc8, //0x000001ac movq         %r13, $-56(%rbp)
	0x4d, 0x29, 0xd5, //0x000001b0 subq         %r10, %r13
	0x0f, 0x84, 0x73, 0x02, 0x00, 0x00, //0x000001b3 je           LBB0_41
	0x4c, 0x8b, 0x1f, //0x000001b9 movq         (%rdi), %r11
	0x49, 0x83, 0xfd, 0x40, //0x000001bc cmpq         $64, %r13
	0x0f, 0x82, 0x90, 0x02, 0x00, 0x00, //0x000001c0 jb           LBB0_43
	0x4c, 0x89, 0xd3, //0x000001c6 movq         %r10, %rbx
	0x48, 0xf7, 0xd3, //0x000001c9 notq         %rbx
	0x48, 0xc7, 0x45, 0xd0, 0xff, 0xff, 0xff, 0xff, //0x000001cc movq         $-1, $-48(%rbp)
	0x45, 0x31, 0xe4, //0x000001d4 xorl         %r12d, %r12d
	0xc5, 0xfe, 0x6f, 0x05, 0x21, 0xfe, 0xff, 0xff, //0x000001d7 vmovdqu      $-479(%rip), %ymm0  /* LCPI0_0+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x0d, 0x39, 0xfe, 0xff, 0xff, //0x000001df vmovdqu      $-455(%rip), %ymm1  /* LCPI0_1+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x15, 0x51, 0xfe, 0xff, 0xff, //0x000001e7 vmovdqu      $-431(%rip), %ymm2  /* LCPI0_2+0(%rip) */
	0xc5, 0xe5, 0x76, 0xdb, //0x000001ef vpcmpeqd     %ymm3, %ymm3, %ymm3
	0x4d, 0x89, 0xd1, //0x000001f3 movq         %r10, %r9
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000001f6 .p2align 4, 0x90
	//0x00000200 LBB0_15
	0xc4, 0x81, 0x7e, 0x6f, 0x24, 0x0b, //0x00000200 vmovdqu      (%r11,%r9), %ymm4
	0xc4, 0x81, 0x7e, 0x6f, 0x6c, 0x0b, 0x20, //0x00000206 vmovdqu      $32(%r11,%r9), %ymm5
	0xc5, 0xdd, 0x74, 0xf0, //0x0000020d vpcmpeqb     %ymm0, %ymm4, %ymm6
	0xc5, 0xfd, 0xd7, 0xce, //0x00000211 vpmovmskb    %ymm6, %ecx
	0xc5, 0xd5, 0x74, 0xf0, //0x00000215 vpcmpeqb     %ymm0, %ymm5, %ymm6
	0xc5, 0x7d, 0xd7, 0xfe, //0x00000219 vpmovmskb    %ymm6, %r15d
	0xc5, 0xdd, 0x74, 0xf1, //0x0000021d vpcmpeqb     %ymm1, %ymm4, %ymm6
	0xc5, 0x7d, 0xd7, 0xf6, //0x00000221 vpmovmskb    %ymm6, %r14d
	0xc5, 0xd5, 0x74, 0xf1, //0x00000225 vpcmpeqb     %ymm1, %ymm5, %ymm6
	0xc5, 0x7d, 0xd7, 0xc6, //0x00000229 vpmovmskb    %ymm6, %r8d
	0xc5, 0xed, 0x64, 0xf5, //0x0000022d vpcmpgtb     %ymm5, %ymm2, %ymm6
	0xc5, 0xd5, 0x64, 0xeb, //0x00000231 vpcmpgtb     %ymm3, %ymm5, %ymm5
	0xc5, 0xcd, 0xdb, 0xed, //0x00000235 vpand        %ymm5, %ymm6, %ymm5
	0xc5, 0xfd, 0xd7, 0xc5, //0x00000239 vpmovmskb    %ymm5, %eax
	0x49, 0xc1, 0xe7, 0x20, //0x0000023d shlq         $32, %r15
	0x4c, 0x09, 0xf9, //0x00000241 orq          %r15, %rcx
	0x49, 0xc1, 0xe0, 0x20, //0x00000244 shlq         $32, %r8
	0x48, 0xc1, 0xe0, 0x20, //0x00000248 shlq         $32, %rax
	0x4d, 0x09, 0xc6, //0x0000024c orq          %r8, %r14
	0x0f, 0x85, 0x4c, 0x00, 0x00, 0x00, //0x0000024f jne          LBB0_21
	0x4d, 0x85, 0xe4, //0x00000255 testq        %r12, %r12
	0x0f, 0x85, 0x59, 0x00, 0x00, 0x00, //0x00000258 jne          LBB0_23
	0x45, 0x31, 0xe4, //0x0000025e xorl         %r12d, %r12d
	//0x00000261 LBB0_18
	0xc5, 0xed, 0x64, 0xec, //0x00000261 vpcmpgtb     %ymm4, %ymm2, %ymm5
	0xc5, 0xdd, 0x64, 0xe3, //0x00000265 vpcmpgtb     %ymm3, %ymm4, %ymm4
	0xc5, 0xd5, 0xdb, 0xe4, //0x00000269 vpand        %ymm4, %ymm5, %ymm4
	0xc5, 0xfd, 0xd7, 0xfc, //0x0000026d vpmovmskb    %ymm4, %edi
	0x48, 0x09, 0xf8, //0x00000271 orq          %rdi, %rax
	0x48, 0x85, 0xc9, //0x00000274 testq        %rcx, %rcx
	0x0f, 0x85, 0x8a, 0x00, 0x00, 0x00, //0x00000277 jne          LBB0_24
	0x48, 0x85, 0xc0, //0x0000027d testq        %rax, %rax
	0x0f, 0x85, 0x3e, 0x04, 0x00, 0x00, //0x00000280 jne          LBB0_80
	0x49, 0x83, 0xc5, 0xc0, //0x00000286 addq         $-64, %r13
	0x48, 0x83, 0xc3, 0xc0, //0x0000028a addq         $-64, %rbx
	0x49, 0x83, 0xc1, 0x40, //0x0000028e addq         $64, %r9
	0x49, 0x83, 0xfd, 0x3f, //0x00000292 cmpq         $63, %r13
	0x0f, 0x87, 0x64, 0xff, 0xff, 0xff, //0x00000296 ja           LBB0_15
	0xe9, 0x23, 0x01, 0x00, 0x00, //0x0000029c jmp          LBB0_35
	//0x000002a1 LBB0_21
	0x48, 0x83, 0x7d, 0xd0, 0xff, //0x000002a1 cmpq         $-1, $-48(%rbp)
	0x0f, 0x85, 0x0b, 0x00, 0x00, 0x00, //0x000002a6 jne          LBB0_23
	0x49, 0x0f, 0xbc, 0xfe, //0x000002ac bsfq         %r14, %rdi
	0x4c, 0x01, 0xcf, //0x000002b0 addq         %r9, %rdi
	0x48, 0x89, 0x7d, 0xd0, //0x000002b3 movq         %rdi, $-48(%rbp)
	//0x000002b7 LBB0_23
	0x4d, 0x89, 0xe0, //0x000002b7 movq         %r12, %r8
	0x49, 0xf7, 0xd0, //0x000002ba notq         %r8
	0x4d, 0x21, 0xf0, //0x000002bd andq         %r14, %r8
	0x4f, 0x8d, 0x3c, 0x00, //0x000002c0 leaq         (%r8,%r8), %r15
	0x4d, 0x09, 0xe7, //0x000002c4 orq          %r12, %r15
	0x4c, 0x89, 0x7d, 0xc0, //0x000002c7 movq         %r15, $-64(%rbp)
	0x49, 0xf7, 0xd7, //0x000002cb notq         %r15
	0x4d, 0x21, 0xf7, //0x000002ce andq         %r14, %r15
	0x48, 0xbf, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x000002d1 movabsq      $-6148914691236517206, %rdi
	0x49, 0x21, 0xff, //0x000002db andq         %rdi, %r15
	0x45, 0x31, 0xe4, //0x000002de xorl         %r12d, %r12d
	0x4d, 0x01, 0xc7, //0x000002e1 addq         %r8, %r15
	0x41, 0x0f, 0x92, 0xc4, //0x000002e4 setb         %r12b
	0x4d, 0x01, 0xff, //0x000002e8 addq         %r15, %r15
	0x48, 0xbf, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000002eb movabsq      $6148914691236517205, %rdi
	0x49, 0x31, 0xff, //0x000002f5 xorq         %rdi, %r15
	0x4c, 0x23, 0x7d, 0xc0, //0x000002f8 andq         $-64(%rbp), %r15
	0x49, 0xf7, 0xd7, //0x000002fc notq         %r15
	0x4c, 0x21, 0xf9, //0x000002ff andq         %r15, %rcx
	0xe9, 0x5a, 0xff, 0xff, 0xff, //0x00000302 jmp          LBB0_18
	//0x00000307 LBB0_24
	0x4c, 0x0f, 0xbc, 0xf1, //0x00000307 bsfq         %rcx, %r14
	0x48, 0x85, 0xc0, //0x0000030b testq        %rax, %rax
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x0000030e je           LBB0_26
	0x48, 0x0f, 0xbc, 0xc0, //0x00000314 bsfq         %rax, %rax
	0x4c, 0x39, 0xf0, //0x00000318 cmpq         %r14, %rax
	0x0f, 0x83, 0x13, 0x00, 0x00, 0x00, //0x0000031b jae          LBB0_27
	0xe9, 0x9e, 0x03, 0x00, 0x00, //0x00000321 jmp          LBB0_80
	//0x00000326 LBB0_26
	0xb8, 0x40, 0x00, 0x00, 0x00, //0x00000326 movl         $64, %eax
	0x4c, 0x39, 0xf0, //0x0000032b cmpq         %r14, %rax
	0x0f, 0x82, 0x90, 0x03, 0x00, 0x00, //0x0000032e jb           LBB0_80
	//0x00000334 LBB0_27
	0x49, 0x29, 0xde, //0x00000334 subq         %rbx, %r14
	//0x00000337 LBB0_28
	0x4d, 0x85, 0xf6, //0x00000337 testq        %r14, %r14
	0x0f, 0x88, 0x8b, 0x03, 0x00, 0x00, //0x0000033a js           LBB0_81
	0x4c, 0x89, 0x36, //0x00000340 movq         %r14, (%rsi)
	0x4c, 0x89, 0x52, 0x10, //0x00000343 movq         %r10, $16(%rdx)
	0x48, 0xc7, 0x02, 0x07, 0x00, 0x00, 0x00, //0x00000347 movq         $7, (%rdx)
	0x48, 0x8b, 0x4d, 0xd0, //0x0000034e movq         $-48(%rbp), %rcx
	0x4c, 0x39, 0xf1, //0x00000352 cmpq         %r14, %rcx
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000355 movq         $-1, %rax
	0x48, 0x0f, 0x4c, 0xc1, //0x0000035c cmovlq       %rcx, %rax
	0x48, 0x89, 0x42, 0x18, //0x00000360 movq         %rax, $24(%rdx)
	0xe9, 0x6c, 0x03, 0x00, 0x00, //0x00000364 jmp          LBB0_83
	//0x00000369 LBB0_30
	0x4d, 0x01, 0xd8, //0x00000369 addq         %r11, %r8
	0x49, 0x83, 0xfd, 0x20, //0x0000036c cmpq         $32, %r13
	0x0f, 0x82, 0x5b, 0x01, 0x00, 0x00, //0x00000370 jb           LBB0_48
	//0x00000376 LBB0_31
	0xc4, 0xc1, 0x7e, 0x6f, 0x00, //0x00000376 vmovdqu      (%r8), %ymm0
	0xc5, 0xfd, 0x74, 0x0d, 0x7d, 0xfc, 0xff, 0xff, //0x0000037b vpcmpeqb     $-899(%rip), %ymm0, %ymm1  /* LCPI0_0+0(%rip) */
	0xc5, 0xfd, 0xd7, 0xf9, //0x00000383 vpmovmskb    %ymm1, %edi
	0xc5, 0xfd, 0x74, 0x05, 0x91, 0xfc, 0xff, 0xff, //0x00000387 vpcmpeqb     $-879(%rip), %ymm0, %ymm0  /* LCPI0_1+0(%rip) */
	0xc5, 0xfd, 0xd7, 0xc0, //0x0000038f vpmovmskb    %ymm0, %eax
	0x85, 0xc0, //0x00000393 testl        %eax, %eax
	0x0f, 0x85, 0xd9, 0x00, 0x00, 0x00, //0x00000395 jne          LBB0_44
	0x4d, 0x85, 0xe4, //0x0000039b testq        %r12, %r12
	0x0f, 0x85, 0xec, 0x00, 0x00, 0x00, //0x0000039e jne          LBB0_46
	0x45, 0x31, 0xe4, //0x000003a4 xorl         %r12d, %r12d
	0x48, 0x85, 0xff, //0x000003a7 testq        %rdi, %rdi
	0x0f, 0x84, 0x19, 0x01, 0x00, 0x00, //0x000003aa je           LBB0_47
	//0x000003b0 LBB0_34
	0x48, 0x0f, 0xbc, 0xc7, //0x000003b0 bsfq         %rdi, %rax
	0x4d, 0x29, 0xd8, //0x000003b4 subq         %r11, %r8
	0x4d, 0x8d, 0x34, 0x00, //0x000003b7 leaq         (%r8,%rax), %r14
	0x49, 0x83, 0xc6, 0x01, //0x000003bb addq         $1, %r14
	0xe9, 0x73, 0xff, 0xff, 0xff, //0x000003bf jmp          LBB0_28
	//0x000003c4 LBB0_35
	0x4d, 0x01, 0xd9, //0x000003c4 addq         %r11, %r9
	0x49, 0x83, 0xfd, 0x20, //0x000003c7 cmpq         $32, %r13
	0x0f, 0x82, 0x6b, 0x02, 0x00, 0x00, //0x000003cb jb           LBB0_70
	//0x000003d1 LBB0_36
	0xc4, 0xc1, 0x7e, 0x6f, 0x09, //0x000003d1 vmovdqu      (%r9), %ymm1
	0xc5, 0xf5, 0x74, 0x05, 0x22, 0xfc, 0xff, 0xff, //0x000003d6 vpcmpeqb     $-990(%rip), %ymm1, %ymm0  /* LCPI0_0+0(%rip) */
	0xc5, 0xfd, 0xd7, 0xc8, //0x000003de vpmovmskb    %ymm0, %ecx
	0xc5, 0xf5, 0x74, 0x05, 0x36, 0xfc, 0xff, 0xff, //0x000003e2 vpcmpeqb     $-970(%rip), %ymm1, %ymm0  /* LCPI0_1+0(%rip) */
	0xc5, 0xfd, 0xd7, 0xc0, //0x000003ea vpmovmskb    %ymm0, %eax
	0xc5, 0xfe, 0x6f, 0x05, 0x4a, 0xfc, 0xff, 0xff, //0x000003ee vmovdqu      $-950(%rip), %ymm0  /* LCPI0_2+0(%rip) */
	0xc5, 0xfd, 0x64, 0xc1, //0x000003f6 vpcmpgtb     %ymm1, %ymm0, %ymm0
	0xc5, 0xed, 0x76, 0xd2, //0x000003fa vpcmpeqd     %ymm2, %ymm2, %ymm2
	0xc5, 0xf5, 0x64, 0xca, //0x000003fe vpcmpgtb     %ymm2, %ymm1, %ymm1
	0x85, 0xc0, //0x00000402 testl        %eax, %eax
	0x0f, 0x85, 0x91, 0x01, 0x00, 0x00, //0x00000404 jne          LBB0_61
	0x4d, 0x85, 0xe4, //0x0000040a testq        %r12, %r12
	0x0f, 0x85, 0xa4, 0x01, 0x00, 0x00, //0x0000040d jne          LBB0_63
	0x45, 0x31, 0xe4, //0x00000413 xorl         %r12d, %r12d
	0xc5, 0xfd, 0xdb, 0xc1, //0x00000416 vpand        %ymm1, %ymm0, %ymm0
	0x48, 0x85, 0xc9, //0x0000041a testq        %rcx, %rcx
	0x0f, 0x84, 0xd1, 0x01, 0x00, 0x00, //0x0000041d je           LBB0_64
	//0x00000423 LBB0_39
	0x48, 0x0f, 0xbc, 0xc1, //0x00000423 bsfq         %rcx, %rax
	0xe9, 0xcd, 0x01, 0x00, 0x00, //0x00000427 jmp          LBB0_65
	//0x0000042c LBB0_41
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x0000042c movq         $-1, %r14
	0xe9, 0x97, 0x02, 0x00, 0x00, //0x00000433 jmp          LBB0_82
	//0x00000438 LBB0_42
	0x4f, 0x8d, 0x04, 0x13, //0x00000438 leaq         (%r11,%r10), %r8
	0x48, 0xc7, 0x45, 0xd0, 0xff, 0xff, 0xff, 0xff, //0x0000043c movq         $-1, $-48(%rbp)
	0x45, 0x31, 0xe4, //0x00000444 xorl         %r12d, %r12d
	0x49, 0x83, 0xfd, 0x20, //0x00000447 cmpq         $32, %r13
	0x0f, 0x83, 0x25, 0xff, 0xff, 0xff, //0x0000044b jae          LBB0_31
	0xe9, 0x7b, 0x00, 0x00, 0x00, //0x00000451 jmp          LBB0_48
	//0x00000456 LBB0_43
	0x4f, 0x8d, 0x0c, 0x13, //0x00000456 leaq         (%r11,%r10), %r9
	0x48, 0xc7, 0x45, 0xd0, 0xff, 0xff, 0xff, 0xff, //0x0000045a movq         $-1, $-48(%rbp)
	0x45, 0x31, 0xe4, //0x00000462 xorl         %r12d, %r12d
	0x49, 0x83, 0xfd, 0x20, //0x00000465 cmpq         $32, %r13
	0x0f, 0x83, 0x62, 0xff, 0xff, 0xff, //0x00000469 jae          LBB0_36
	0xe9, 0xc8, 0x01, 0x00, 0x00, //0x0000046f jmp          LBB0_70
	//0x00000474 LBB0_44
	0x48, 0x83, 0x7d, 0xd0, 0xff, //0x00000474 cmpq         $-1, $-48(%rbp)
	0x0f, 0x85, 0x11, 0x00, 0x00, 0x00, //0x00000479 jne          LBB0_46
	0x4c, 0x89, 0xc1, //0x0000047f movq         %r8, %rcx
	0x4c, 0x29, 0xd9, //0x00000482 subq         %r11, %rcx
	0x48, 0x0f, 0xbc, 0xd8, //0x00000485 bsfq         %rax, %rbx
	0x48, 0x01, 0xcb, //0x00000489 addq         %rcx, %rbx
	0x48, 0x89, 0x5d, 0xd0, //0x0000048c movq         %rbx, $-48(%rbp)
	//0x00000490 LBB0_46
	0x44, 0x89, 0xe1, //0x00000490 movl         %r12d, %ecx
	0xf7, 0xd1, //0x00000493 notl         %ecx
	0x21, 0xc1, //0x00000495 andl         %eax, %ecx
	0x8d, 0x1c, 0x09, //0x00000497 leal         (%rcx,%rcx), %ebx
	0x45, 0x8d, 0x0c, 0x4c, //0x0000049a leal         (%r12,%rcx,2), %r9d
	0xf7, 0xd3, //0x0000049e notl         %ebx
	0x21, 0xc3, //0x000004a0 andl         %eax, %ebx
	0x81, 0xe3, 0xaa, 0xaa, 0xaa, 0xaa, //0x000004a2 andl         $-1431655766, %ebx
	0x45, 0x31, 0xe4, //0x000004a8 xorl         %r12d, %r12d
	0x01, 0xcb, //0x000004ab addl         %ecx, %ebx
	0x41, 0x0f, 0x92, 0xc4, //0x000004ad setb         %r12b
	0x01, 0xdb, //0x000004b1 addl         %ebx, %ebx
	0x81, 0xf3, 0x55, 0x55, 0x55, 0x55, //0x000004b3 xorl         $1431655765, %ebx
	0x44, 0x21, 0xcb, //0x000004b9 andl         %r9d, %ebx
	0xf7, 0xd3, //0x000004bc notl         %ebx
	0x21, 0xdf, //0x000004be andl         %ebx, %edi
	0x48, 0x85, 0xff, //0x000004c0 testq        %rdi, %rdi
	0x0f, 0x85, 0xe7, 0xfe, 0xff, 0xff, //0x000004c3 jne          LBB0_34
	//0x000004c9 LBB0_47
	0x49, 0x83, 0xc0, 0x20, //0x000004c9 addq         $32, %r8
	0x49, 0x83, 0xc5, 0xe0, //0x000004cd addq         $-32, %r13
	//0x000004d1 LBB0_48
	0x4d, 0x85, 0xe4, //0x000004d1 testq        %r12, %r12
	0x0f, 0x85, 0x1c, 0x02, 0x00, 0x00, //0x000004d4 jne          LBB0_85
	0x4c, 0x89, 0xdf, //0x000004da movq         %r11, %rdi
	0x48, 0xf7, 0xd7, //0x000004dd notq         %rdi
	0x4c, 0x8b, 0x7d, 0xd0, //0x000004e0 movq         $-48(%rbp), %r15
	0x4d, 0x85, 0xed, //0x000004e4 testq        %r13, %r13
	0x0f, 0x84, 0x8b, 0x00, 0x00, 0x00, //0x000004e7 je           LBB0_58
	//0x000004ed LBB0_50
	0x48, 0x83, 0xc7, 0x01, //0x000004ed addq         $1, %rdi
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x000004f1 movq         $-1, %r14
	//0x000004f8 LBB0_51
	0x31, 0xc0, //0x000004f8 xorl         %eax, %eax
	//0x000004fa LBB0_52
	0x41, 0x0f, 0xb6, 0x1c, 0x00, //0x000004fa movzbl       (%r8,%rax), %ebx
	0x80, 0xfb, 0x22, //0x000004ff cmpb         $34, %bl
	0x0f, 0x84, 0x69, 0x00, 0x00, 0x00, //0x00000502 je           LBB0_57
	0x80, 0xfb, 0x5c, //0x00000508 cmpb         $92, %bl
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x0000050b je           LBB0_55
	0x48, 0x83, 0xc0, 0x01, //0x00000511 addq         $1, %rax
	0x49, 0x39, 0xc5, //0x00000515 cmpq         %rax, %r13
	0x0f, 0x85, 0xdc, 0xff, 0xff, 0xff, //0x00000518 jne          LBB0_52
	0xe9, 0x60, 0x00, 0x00, 0x00, //0x0000051e jmp          LBB0_59
	//0x00000523 LBB0_55
	0x49, 0x8d, 0x4d, 0xff, //0x00000523 leaq         $-1(%r13), %rcx
	0x48, 0x39, 0xc1, //0x00000527 cmpq         %rax, %rcx
	0x0f, 0x84, 0x9b, 0x01, 0x00, 0x00, //0x0000052a je           LBB0_81
	0x4a, 0x8d, 0x0c, 0x07, //0x00000530 leaq         (%rdi,%r8), %rcx
	0x48, 0x01, 0xc1, //0x00000534 addq         %rax, %rcx
	0x49, 0x83, 0xff, 0xff, //0x00000537 cmpq         $-1, %r15
	0x48, 0x8b, 0x5d, 0xd0, //0x0000053b movq         $-48(%rbp), %rbx
	0x48, 0x0f, 0x44, 0xd9, //0x0000053f cmoveq       %rcx, %rbx
	0x48, 0x89, 0x5d, 0xd0, //0x00000543 movq         %rbx, $-48(%rbp)
	0x4c, 0x0f, 0x44, 0xf9, //0x00000547 cmoveq       %rcx, %r15
	0x49, 0x01, 0xc0, //0x0000054b addq         %rax, %r8
	0x49, 0x83, 0xc0, 0x02, //0x0000054e addq         $2, %r8
	0x4c, 0x89, 0xe9, //0x00000552 movq         %r13, %rcx
	0x48, 0x29, 0xc1, //0x00000555 subq         %rax, %rcx
	0x48, 0x83, 0xc1, 0xfe, //0x00000558 addq         $-2, %rcx
	0x49, 0x83, 0xc5, 0xfe, //0x0000055c addq         $-2, %r13
	0x49, 0x39, 0xc5, //0x00000560 cmpq         %rax, %r13
	0x49, 0x89, 0xcd, //0x00000563 movq         %rcx, %r13
	0x0f, 0x85, 0x8c, 0xff, 0xff, 0xff, //0x00000566 jne          LBB0_51
	0xe9, 0x5a, 0x01, 0x00, 0x00, //0x0000056c jmp          LBB0_81
	//0x00000571 LBB0_57
	0x49, 0x01, 0xc0, //0x00000571 addq         %rax, %r8
	0x49, 0x83, 0xc0, 0x01, //0x00000574 addq         $1, %r8
	//0x00000578 LBB0_58
	0x4d, 0x29, 0xd8, //0x00000578 subq         %r11, %r8
	0x4d, 0x89, 0xc6, //0x0000057b movq         %r8, %r14
	0xe9, 0xb4, 0xfd, 0xff, 0xff, //0x0000057e jmp          LBB0_28
	//0x00000583 LBB0_59
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x00000583 movq         $-1, %r14
	0x80, 0xfb, 0x22, //0x0000058a cmpb         $34, %bl
	0x0f, 0x85, 0x38, 0x01, 0x00, 0x00, //0x0000058d jne          LBB0_81
	0x4d, 0x01, 0xe8, //0x00000593 addq         %r13, %r8
	0xe9, 0xdd, 0xff, 0xff, 0xff, //0x00000596 jmp          LBB0_58
	//0x0000059b LBB0_61
	0x48, 0x83, 0x7d, 0xd0, 0xff, //0x0000059b cmpq         $-1, $-48(%rbp)
	0x0f, 0x85, 0x11, 0x00, 0x00, 0x00, //0x000005a0 jne          LBB0_63
	0x4c, 0x89, 0xcf, //0x000005a6 movq         %r9, %rdi
	0x4c, 0x29, 0xdf, //0x000005a9 subq         %r11, %rdi
	0x48, 0x0f, 0xbc, 0xd8, //0x000005ac bsfq         %rax, %rbx
	0x48, 0x01, 0xfb, //0x000005b0 addq         %rdi, %rbx
	0x48, 0x89, 0x5d, 0xd0, //0x000005b3 movq         %rbx, $-48(%rbp)
	//0x000005b7 LBB0_63
	0x44, 0x89, 0xe7, //0x000005b7 movl         %r12d, %edi
	0xf7, 0xd7, //0x000005ba notl         %edi
	0x21, 0xc7, //0x000005bc andl         %eax, %edi
	0x8d, 0x1c, 0x3f, //0x000005be leal         (%rdi,%rdi), %ebx
	0x45, 0x8d, 0x04, 0x7c, //0x000005c1 leal         (%r12,%rdi,2), %r8d
	0xf7, 0xd3, //0x000005c5 notl         %ebx
	0x21, 0xc3, //0x000005c7 andl         %eax, %ebx
	0x81, 0xe3, 0xaa, 0xaa, 0xaa, 0xaa, //0x000005c9 andl         $-1431655766, %ebx
	0x45, 0x31, 0xe4, //0x000005cf xorl         %r12d, %r12d
	0x01, 0xfb, //0x000005d2 addl         %edi, %ebx
	0x41, 0x0f, 0x92, 0xc4, //0x000005d4 setb         %r12b
	0x01, 0xdb, //0x000005d8 addl         %ebx, %ebx
	0x81, 0xf3, 0x55, 0x55, 0x55, 0x55, //0x000005da xorl         $1431655765, %ebx
	0x44, 0x21, 0xc3, //0x000005e0 andl         %r8d, %ebx
	0xf7, 0xd3, //0x000005e3 notl         %ebx
	0x21, 0xd9, //0x000005e5 andl         %ebx, %ecx
	0xc5, 0xfd, 0xdb, 0xc1, //0x000005e7 vpand        %ymm1, %ymm0, %ymm0
	0x48, 0x85, 0xc9, //0x000005eb testq        %rcx, %rcx
	0x0f, 0x85, 0x2f, 0xfe, 0xff, 0xff, //0x000005ee jne          LBB0_39
	//0x000005f4 LBB0_64
	0xb8, 0x40, 0x00, 0x00, 0x00, //0x000005f4 movl         $64, %eax
	//0x000005f9 LBB0_65
	0xc5, 0xfd, 0xd7, 0xd8, //0x000005f9 vpmovmskb    %ymm0, %ebx
	0x48, 0x85, 0xc9, //0x000005fd testq        %rcx, %rcx
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x00000600 je           LBB0_68
	0x0f, 0xbc, 0xcb, //0x00000606 bsfl         %ebx, %ecx
	0x85, 0xdb, //0x00000609 testl        %ebx, %ebx
	0xbf, 0x40, 0x00, 0x00, 0x00, //0x0000060b movl         $64, %edi
	0x0f, 0x45, 0xf9, //0x00000610 cmovnel      %ecx, %edi
	0x48, 0x39, 0xf8, //0x00000613 cmpq         %rdi, %rax
	0x0f, 0x87, 0xa8, 0x00, 0x00, 0x00, //0x00000616 ja           LBB0_80
	0x4d, 0x29, 0xd9, //0x0000061c subq         %r11, %r9
	0x4d, 0x8d, 0x34, 0x01, //0x0000061f leaq         (%r9,%rax), %r14
	0x49, 0x83, 0xc6, 0x01, //0x00000623 addq         $1, %r14
	0xe9, 0x0b, 0xfd, 0xff, 0xff, //0x00000627 jmp          LBB0_28
	//0x0000062c LBB0_68
	0x85, 0xdb, //0x0000062c testl        %ebx, %ebx
	0x0f, 0x85, 0x90, 0x00, 0x00, 0x00, //0x0000062e jne          LBB0_80
	0x49, 0x83, 0xc1, 0x20, //0x00000634 addq         $32, %r9
	0x49, 0x83, 0xc5, 0xe0, //0x00000638 addq         $-32, %r13
	//0x0000063c LBB0_70
	0x4d, 0x85, 0xe4, //0x0000063c testq        %r12, %r12
	0x0f, 0x85, 0xf1, 0x00, 0x00, 0x00, //0x0000063f jne          LBB0_87
	0x48, 0x8b, 0x45, 0xd0, //0x00000645 movq         $-48(%rbp), %rax
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x00000649 movq         $-1, %r14
	0x4d, 0x85, 0xed, //0x00000650 testq        %r13, %r13
	0x0f, 0x84, 0x72, 0x00, 0x00, 0x00, //0x00000653 je           LBB0_81
	//0x00000659 LBB0_72
	0x41, 0x0f, 0xb6, 0x09, //0x00000659 movzbl       (%r9), %ecx
	0x80, 0xf9, 0x22, //0x0000065d cmpb         $34, %cl
	0x0f, 0x84, 0x81, 0x00, 0x00, 0x00, //0x00000660 je           LBB0_84
	0x80, 0xf9, 0x5c, //0x00000666 cmpb         $92, %cl
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x00000669 je           LBB0_77
	0x80, 0xf9, 0x20, //0x0000066f cmpb         $32, %cl
	0x0f, 0x82, 0x4c, 0x00, 0x00, 0x00, //0x00000672 jb           LBB0_80
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00000678 movq         $-1, %rcx
	0xbb, 0x01, 0x00, 0x00, 0x00, //0x0000067f movl         $1, %ebx
	//0x00000684 LBB0_76
	0x49, 0x01, 0xd9, //0x00000684 addq         %rbx, %r9
	0x49, 0x01, 0xcd, //0x00000687 addq         %rcx, %r13
	0x0f, 0x85, 0xc9, 0xff, 0xff, 0xff, //0x0000068a jne          LBB0_72
	0xe9, 0x36, 0x00, 0x00, 0x00, //0x00000690 jmp          LBB0_81
	//0x00000695 LBB0_77
	0x49, 0x83, 0xfd, 0x01, //0x00000695 cmpq         $1, %r13
	0x0f, 0x84, 0x2c, 0x00, 0x00, 0x00, //0x00000699 je           LBB0_81
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x0000069f movq         $-2, %rcx
	0xbb, 0x02, 0x00, 0x00, 0x00, //0x000006a6 movl         $2, %ebx
	0x48, 0x83, 0xf8, 0xff, //0x000006ab cmpq         $-1, %rax
	0x0f, 0x85, 0xcf, 0xff, 0xff, 0xff, //0x000006af jne          LBB0_76
	0x4c, 0x89, 0xc8, //0x000006b5 movq         %r9, %rax
	0x4c, 0x29, 0xd8, //0x000006b8 subq         %r11, %rax
	0x48, 0x89, 0x45, 0xd0, //0x000006bb movq         %rax, $-48(%rbp)
	0xe9, 0xc0, 0xff, 0xff, 0xff, //0x000006bf jmp          LBB0_76
	//0x000006c4 LBB0_80
	0x49, 0xc7, 0xc6, 0xfe, 0xff, 0xff, 0xff, //0x000006c4 movq         $-2, %r14
	//0x000006cb LBB0_81
	0x4c, 0x8b, 0x55, 0xc8, //0x000006cb movq         $-56(%rbp), %r10
	//0x000006cf LBB0_82
	0x4c, 0x89, 0x16, //0x000006cf movq         %r10, (%rsi)
	0x4c, 0x89, 0x32, //0x000006d2 movq         %r14, (%rdx)
	//0x000006d5 LBB0_83
	0x48, 0x83, 0xc4, 0x18, //0x000006d5 addq         $24, %rsp
	0x5b, //0x000006d9 popq         %rbx
	0x41, 0x5c, //0x000006da popq         %r12
	0x41, 0x5d, //0x000006dc popq         %r13
	0x41, 0x5e, //0x000006de popq         %r14
	0x41, 0x5f, //0x000006e0 popq         %r15
	0x5d, //0x000006e2 popq         %rbp
	0xc5, 0xf8, 0x77, //0x000006e3 vzeroupper   
	0xc3, //0x000006e6 retq         
	//0x000006e7 LBB0_84
	0x4d, 0x29, 0xd9, //0x000006e7 subq         %r11, %r9
	0x49, 0x83, 0xc1, 0x01, //0x000006ea addq         $1, %r9
	0x4d, 0x89, 0xce, //0x000006ee movq         %r9, %r14
	0xe9, 0x41, 0xfc, 0xff, 0xff, //0x000006f1 jmp          LBB0_28
	//0x000006f6 LBB0_85
	0x4d, 0x85, 0xed, //0x000006f6 testq        %r13, %r13
	0x0f, 0x84, 0x7d, 0x00, 0x00, 0x00, //0x000006f9 je           LBB0_89
	0x4c, 0x89, 0xdf, //0x000006ff movq         %r11, %rdi
	0x48, 0xf7, 0xd7, //0x00000702 notq         %rdi
	0x49, 0x8d, 0x04, 0x38, //0x00000705 leaq         (%r8,%rdi), %rax
	0x48, 0x8b, 0x4d, 0xd0, //0x00000709 movq         $-48(%rbp), %rcx
	0x48, 0x83, 0xf9, 0xff, //0x0000070d cmpq         $-1, %rcx
	0x49, 0x89, 0xcf, //0x00000711 movq         %rcx, %r15
	0x48, 0x0f, 0x44, 0xc8, //0x00000714 cmoveq       %rax, %rcx
	0x4c, 0x0f, 0x44, 0xf8, //0x00000718 cmoveq       %rax, %r15
	0x49, 0x83, 0xc0, 0x01, //0x0000071c addq         $1, %r8
	0x49, 0x83, 0xc5, 0xff, //0x00000720 addq         $-1, %r13
	0x48, 0x89, 0x4d, 0xd0, //0x00000724 movq         %rcx, $-48(%rbp)
	0x4d, 0x85, 0xed, //0x00000728 testq        %r13, %r13
	0x0f, 0x85, 0xbc, 0xfd, 0xff, 0xff, //0x0000072b jne          LBB0_50
	0xe9, 0x42, 0xfe, 0xff, 0xff, //0x00000731 jmp          LBB0_58
	//0x00000736 LBB0_87
	0x4d, 0x85, 0xed, //0x00000736 testq        %r13, %r13
	0x0f, 0x84, 0x3d, 0x00, 0x00, 0x00, //0x00000739 je           LBB0_89
	0x4c, 0x89, 0xd8, //0x0000073f movq         %r11, %rax
	0x48, 0xf7, 0xd0, //0x00000742 notq         %rax
	0x4c, 0x01, 0xc8, //0x00000745 addq         %r9, %rax
	0x48, 0x8b, 0x7d, 0xd0, //0x00000748 movq         $-48(%rbp), %rdi
	0x48, 0x83, 0xff, 0xff, //0x0000074c cmpq         $-1, %rdi
	0x48, 0x89, 0xf9, //0x00000750 movq         %rdi, %rcx
	0x48, 0x0f, 0x44, 0xc8, //0x00000753 cmoveq       %rax, %rcx
	0x48, 0x0f, 0x45, 0xc7, //0x00000757 cmovneq      %rdi, %rax
	0x49, 0x83, 0xc1, 0x01, //0x0000075b addq         $1, %r9
	0x49, 0x83, 0xc5, 0xff, //0x0000075f addq         $-1, %r13
	0x48, 0x89, 0x4d, 0xd0, //0x00000763 movq         %rcx, $-48(%rbp)
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x00000767 movq         $-1, %r14
	0x4d, 0x85, 0xed, //0x0000076e testq        %r13, %r13
	0x0f, 0x85, 0xe2, 0xfe, 0xff, 0xff, //0x00000771 jne          LBB0_72
	0xe9, 0x4f, 0xff, 0xff, 0xff, //0x00000777 jmp          LBB0_81
	//0x0000077c LBB0_89
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x0000077c movq         $-1, %r14
	0xe9, 0x43, 0xff, 0xff, 0xff, //0x00000783 jmp          LBB0_81
	//0x00000788 .p2align 2, 0x00
	//0x00000788 _MASK_USE_NUMBER
	0x02, 0x00, 0x00, 0x00, //0x00000788 .long 2
}
 
