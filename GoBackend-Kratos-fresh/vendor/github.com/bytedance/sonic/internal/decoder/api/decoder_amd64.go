//go:build go1.17 && !go1.25
// +build go1.17,!go1.25

/*
 * Copyright 2021 ByteDance Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package api

import (
	"github.com/bytedance/sonic/internal/envs"
	"github.com/bytedance/sonic/internal/decoder/jitdec"
	"github.com/bytedance/sonic/internal/decoder/optdec"
)

var (
	pretouchImpl = jitdec.Pretouch
	decodeImpl = jitdec.Decode
) 

 func init() {
	if envs.UseOptDec {
		pretouchImpl = optdec.Pretouch
		decodeImpl = optdec.Decode
	}
 }
