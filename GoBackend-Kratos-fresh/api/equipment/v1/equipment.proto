syntax = "proto3";

package api.equipment.v1;

import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/struct.proto";

option go_package = "gobackend-hvac-kratos/api/equipment/v1;v1";

// 🏭 Equipment Registry Service - Comprehensive Equipment Management
service EquipmentService {
  // Equipment CRUD Operations
  rpc CreateEquipment(CreateEquipmentRequest) returns (CreateEquipmentResponse) {
    option (google.api.http) = {
      post: "/api/v1/equipment"
      body: "*"
    };
  }

  rpc GetEquipment(GetEquipmentRequest) returns (GetEquipmentResponse) {
    option (google.api.http) = {
      get: "/api/v1/equipment/{id}"
    };
  }

  rpc ListEquipment(ListEquipmentRequest) returns (ListEquipmentResponse) {
    option (google.api.http) = {
      get: "/api/v1/equipment"
    };
  }

  rpc UpdateEquipment(UpdateEquipmentRequest) returns (UpdateEquipmentResponse) {
    option (google.api.http) = {
      put: "/api/v1/equipment/{id}"
      body: "*"
    };
  }

  rpc DeleteEquipment(DeleteEquipmentRequest) returns (DeleteEquipmentResponse) {
    option (google.api.http) = {
      delete: "/api/v1/equipment/{id}"
    };
  }

  // Health Monitoring
  rpc GetEquipmentHealth(GetEquipmentHealthRequest) returns (GetEquipmentHealthResponse) {
    option (google.api.http) = {
      get: "/api/v1/equipment/{id}/health"
    };
  }

  rpc UpdateEquipmentHealth(UpdateEquipmentHealthRequest) returns (UpdateEquipmentHealthResponse) {
    option (google.api.http) = {
      post: "/api/v1/equipment/{id}/health"
      body: "*"
    };
  }

  // Maintenance Management
  rpc ScheduleMaintenance(ScheduleMaintenanceRequest) returns (ScheduleMaintenanceResponse) {
    option (google.api.http) = {
      post: "/api/v1/equipment/{id}/maintenance/schedule"
      body: "*"
    };
  }

  rpc GetMaintenanceHistory(GetMaintenanceHistoryRequest) returns (GetMaintenanceHistoryResponse) {
    option (google.api.http) = {
      get: "/api/v1/equipment/{id}/maintenance/history"
    };
  }

  // Parts Management
  rpc GetEquipmentParts(GetEquipmentPartsRequest) returns (GetEquipmentPartsResponse) {
    option (google.api.http) = {
      get: "/api/v1/equipment/{id}/parts"
    };
  }

  rpc UpdatePartStatus(UpdatePartStatusRequest) returns (UpdatePartStatusResponse) {
    option (google.api.http) = {
      put: "/api/v1/equipment/{id}/parts/{part_id}"
      body: "*"
    };
  }

  // Analytics and Reporting
  rpc GetEquipmentAnalytics(GetEquipmentAnalyticsRequest) returns (GetEquipmentAnalyticsResponse) {
    option (google.api.http) = {
      get: "/api/v1/equipment/{id}/analytics"
    };
  }

  rpc GetFleetOverview(GetFleetOverviewRequest) returns (GetFleetOverviewResponse) {
    option (google.api.http) = {
      get: "/api/v1/equipment/fleet/overview"
    };
  }

  // QR Code and Identification
  rpc GenerateQRCode(GenerateQRCodeRequest) returns (GenerateQRCodeResponse) {
    option (google.api.http) = {
      post: "/api/v1/equipment/{id}/qr-code"
      body: "*"
    };
  }
}

// ============================================================================
// EQUIPMENT MODELS
// ============================================================================

message Equipment {
  int64 id = 1;
  int64 customer_id = 2;
  string name = 3;
  string type = 4; // hvac_unit, heat_pump, air_conditioner, furnace, etc.
  string brand = 5;
  string model = 6;
  string serial_number = 7;
  google.protobuf.Timestamp installation_date = 8;
  google.protobuf.Timestamp warranty_expiry = 9;
  string location = 10; // Physical location description
  double latitude = 11;
  double longitude = 12;
  EquipmentStatus status = 13;
  EquipmentHealth health = 14;
  google.protobuf.Struct specifications = 15; // Technical specifications
  google.protobuf.Struct metadata = 16; // Additional metadata
  google.protobuf.Timestamp created_at = 17;
  google.protobuf.Timestamp updated_at = 18;
}

enum EquipmentStatus {
  EQUIPMENT_STATUS_UNSPECIFIED = 0;
  EQUIPMENT_STATUS_ACTIVE = 1;
  EQUIPMENT_STATUS_INACTIVE = 2;
  EQUIPMENT_STATUS_MAINTENANCE = 3;
  EQUIPMENT_STATUS_RETIRED = 4;
  EQUIPMENT_STATUS_FAULTY = 5;
}

message EquipmentHealth {
  double health_score = 1; // 0.0 to 1.0
  string health_status = 2; // excellent, good, fair, poor, critical
  repeated HealthMetric metrics = 3;
  google.protobuf.Timestamp last_assessment = 4;
  string assessment_method = 5; // manual, iot_sensors, predictive_model
}

message HealthMetric {
  string name = 1;
  double value = 2;
  string unit = 3;
  double threshold_min = 4;
  double threshold_max = 5;
  string status = 6; // normal, warning, critical
}

// ============================================================================
// REQUEST/RESPONSE MESSAGES
// ============================================================================

// Equipment CRUD
message CreateEquipmentRequest {
  int64 customer_id = 1;
  string name = 2;
  string type = 3;
  string brand = 4;
  string model = 5;
  string serial_number = 6;
  google.protobuf.Timestamp installation_date = 7;
  google.protobuf.Timestamp warranty_expiry = 8;
  string location = 9;
  double latitude = 10;
  double longitude = 11;
  google.protobuf.Struct specifications = 12;
  google.protobuf.Struct metadata = 13;
}

message CreateEquipmentResponse {
  Equipment equipment = 1;
}

message GetEquipmentRequest {
  int64 id = 1;
}

message GetEquipmentResponse {
  Equipment equipment = 1;
}

message ListEquipmentRequest {
  int32 page = 1;
  int32 page_size = 2;
  int64 customer_id = 3;
  string type = 4;
  string status = 5;
  string health_status = 6;
}

message ListEquipmentResponse {
  repeated Equipment equipment = 1;
  int32 total_count = 2;
  int32 page = 3;
  int32 page_size = 4;
}

message UpdateEquipmentRequest {
  int64 id = 1;
  string name = 2;
  string location = 3;
  double latitude = 4;
  double longitude = 5;
  EquipmentStatus status = 6;
  google.protobuf.Struct specifications = 7;
  google.protobuf.Struct metadata = 8;
}

message UpdateEquipmentResponse {
  Equipment equipment = 1;
}

message DeleteEquipmentRequest {
  int64 id = 1;
}

message DeleteEquipmentResponse {
  bool success = 1;
}

// Health Monitoring
message GetEquipmentHealthRequest {
  int64 id = 1;
}

message GetEquipmentHealthResponse {
  EquipmentHealth health = 1;
  repeated HealthTrend trends = 2;
}

message UpdateEquipmentHealthRequest {
  int64 id = 1;
  repeated HealthMetric metrics = 2;
  string assessment_method = 3;
}

message UpdateEquipmentHealthResponse {
  EquipmentHealth health = 1;
}

message HealthTrend {
  string metric_name = 1;
  repeated HealthDataPoint data_points = 2;
}

message HealthDataPoint {
  google.protobuf.Timestamp timestamp = 1;
  double value = 2;
}

// Maintenance Management
message ScheduleMaintenanceRequest {
  int64 id = 1;
  string maintenance_type = 2; // preventive, corrective, emergency
  google.protobuf.Timestamp scheduled_date = 3;
  string description = 4;
  int64 technician_id = 5;
  repeated string required_parts = 6;
  int32 estimated_duration_minutes = 7;
}

message ScheduleMaintenanceResponse {
  MaintenanceSchedule schedule = 1;
}

message GetMaintenanceHistoryRequest {
  int64 id = 1;
  int32 page = 2;
  int32 page_size = 3;
  string maintenance_type = 4;
}

message GetMaintenanceHistoryResponse {
  repeated MaintenanceRecord records = 1;
  int32 total_count = 2;
}

message MaintenanceSchedule {
  int64 id = 1;
  int64 equipment_id = 2;
  string maintenance_type = 3;
  google.protobuf.Timestamp scheduled_date = 4;
  string description = 5;
  int64 technician_id = 6;
  repeated string required_parts = 7;
  int32 estimated_duration_minutes = 8;
  string status = 9; // scheduled, in_progress, completed, cancelled
  google.protobuf.Timestamp created_at = 10;
}

message MaintenanceRecord {
  int64 id = 1;
  int64 equipment_id = 2;
  string maintenance_type = 3;
  google.protobuf.Timestamp performed_date = 4;
  string description = 5;
  int64 technician_id = 6;
  repeated string parts_used = 7;
  int32 actual_duration_minutes = 8;
  string status = 9;
  string notes = 10;
  double cost = 11;
  google.protobuf.Timestamp created_at = 12;
}

// Parts Management
message GetEquipmentPartsRequest {
  int64 id = 1;
}

message GetEquipmentPartsResponse {
  repeated EquipmentPart parts = 1;
}

message UpdatePartStatusRequest {
  int64 id = 1;
  int64 part_id = 2;
  string status = 3;
  google.protobuf.Timestamp replacement_date = 4;
  string notes = 5;
}

message UpdatePartStatusResponse {
  EquipmentPart part = 1;
}

message EquipmentPart {
  int64 id = 1;
  int64 equipment_id = 2;
  string name = 3;
  string part_number = 4;
  string manufacturer = 5;
  google.protobuf.Timestamp installation_date = 6;
  google.protobuf.Timestamp warranty_expiry = 7;
  string status = 8; // new, good, worn, needs_replacement, replaced
  double cost = 9;
  string supplier = 10;
  google.protobuf.Struct specifications = 11;
}

// Analytics and Reporting
message GetEquipmentAnalyticsRequest {
  int64 id = 1;
  google.protobuf.Timestamp start_date = 2;
  google.protobuf.Timestamp end_date = 3;
}

message GetEquipmentAnalyticsResponse {
  EquipmentAnalytics analytics = 1;
}

message GetFleetOverviewRequest {
  int64 customer_id = 1; // Optional: filter by customer
  string equipment_type = 2; // Optional: filter by type
}

message GetFleetOverviewResponse {
  FleetOverview overview = 1;
}

message EquipmentAnalytics {
  int64 equipment_id = 1;
  double uptime_percentage = 2;
  int32 maintenance_count = 3;
  double total_maintenance_cost = 4;
  double energy_efficiency_score = 5;
  repeated PerformanceMetric performance_metrics = 6;
  google.protobuf.Timestamp analysis_period_start = 7;
  google.protobuf.Timestamp analysis_period_end = 8;
}

message FleetOverview {
  int32 total_equipment = 1;
  int32 active_equipment = 2;
  int32 maintenance_due = 3;
  int32 critical_health = 4;
  double average_health_score = 5;
  double total_fleet_value = 6;
  repeated EquipmentTypeSummary type_summaries = 7;
}

message EquipmentTypeSummary {
  string equipment_type = 1;
  int32 count = 2;
  double average_health_score = 3;
  int32 maintenance_due = 4;
}

message PerformanceMetric {
  string name = 1;
  double value = 2;
  string unit = 3;
  double benchmark = 4;
  string trend = 5; // improving, stable, declining
}

// QR Code Generation
message GenerateQRCodeRequest {
  int64 id = 1;
  string format = 2; // png, svg, pdf
  int32 size = 3; // Size in pixels
}

message GenerateQRCodeResponse {
  string qr_code_url = 1;
  bytes qr_code_data = 2;
  string format = 3;
}
