// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v3.21.12
// source: equipment/v1/equipment.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationEquipmentServiceCreateEquipment = "/api.equipment.v1.EquipmentService/CreateEquipment"
const OperationEquipmentServiceDeleteEquipment = "/api.equipment.v1.EquipmentService/DeleteEquipment"
const OperationEquipmentServiceGenerateQRCode = "/api.equipment.v1.EquipmentService/GenerateQRCode"
const OperationEquipmentServiceGetEquipment = "/api.equipment.v1.EquipmentService/GetEquipment"
const OperationEquipmentServiceGetEquipmentAnalytics = "/api.equipment.v1.EquipmentService/GetEquipmentAnalytics"
const OperationEquipmentServiceGetEquipmentHealth = "/api.equipment.v1.EquipmentService/GetEquipmentHealth"
const OperationEquipmentServiceGetEquipmentParts = "/api.equipment.v1.EquipmentService/GetEquipmentParts"
const OperationEquipmentServiceGetFleetOverview = "/api.equipment.v1.EquipmentService/GetFleetOverview"
const OperationEquipmentServiceGetMaintenanceHistory = "/api.equipment.v1.EquipmentService/GetMaintenanceHistory"
const OperationEquipmentServiceListEquipment = "/api.equipment.v1.EquipmentService/ListEquipment"
const OperationEquipmentServiceScheduleMaintenance = "/api.equipment.v1.EquipmentService/ScheduleMaintenance"
const OperationEquipmentServiceUpdateEquipment = "/api.equipment.v1.EquipmentService/UpdateEquipment"
const OperationEquipmentServiceUpdateEquipmentHealth = "/api.equipment.v1.EquipmentService/UpdateEquipmentHealth"
const OperationEquipmentServiceUpdatePartStatus = "/api.equipment.v1.EquipmentService/UpdatePartStatus"

type EquipmentServiceHTTPServer interface {
	// CreateEquipment Equipment CRUD Operations
	CreateEquipment(context.Context, *CreateEquipmentRequest) (*CreateEquipmentResponse, error)
	DeleteEquipment(context.Context, *DeleteEquipmentRequest) (*DeleteEquipmentResponse, error)
	// GenerateQRCode QR Code and Identification
	GenerateQRCode(context.Context, *GenerateQRCodeRequest) (*GenerateQRCodeResponse, error)
	GetEquipment(context.Context, *GetEquipmentRequest) (*GetEquipmentResponse, error)
	// GetEquipmentAnalytics Analytics and Reporting
	GetEquipmentAnalytics(context.Context, *GetEquipmentAnalyticsRequest) (*GetEquipmentAnalyticsResponse, error)
	// GetEquipmentHealth Health Monitoring
	GetEquipmentHealth(context.Context, *GetEquipmentHealthRequest) (*GetEquipmentHealthResponse, error)
	// GetEquipmentParts Parts Management
	GetEquipmentParts(context.Context, *GetEquipmentPartsRequest) (*GetEquipmentPartsResponse, error)
	GetFleetOverview(context.Context, *GetFleetOverviewRequest) (*GetFleetOverviewResponse, error)
	GetMaintenanceHistory(context.Context, *GetMaintenanceHistoryRequest) (*GetMaintenanceHistoryResponse, error)
	ListEquipment(context.Context, *ListEquipmentRequest) (*ListEquipmentResponse, error)
	// ScheduleMaintenance Maintenance Management
	ScheduleMaintenance(context.Context, *ScheduleMaintenanceRequest) (*ScheduleMaintenanceResponse, error)
	UpdateEquipment(context.Context, *UpdateEquipmentRequest) (*UpdateEquipmentResponse, error)
	UpdateEquipmentHealth(context.Context, *UpdateEquipmentHealthRequest) (*UpdateEquipmentHealthResponse, error)
	UpdatePartStatus(context.Context, *UpdatePartStatusRequest) (*UpdatePartStatusResponse, error)
}

func RegisterEquipmentServiceHTTPServer(s *http.Server, srv EquipmentServiceHTTPServer) {
	r := s.Route("/")
	r.POST("/api/v1/equipment", _EquipmentService_CreateEquipment0_HTTP_Handler(srv))
	r.GET("/api/v1/equipment/{id}", _EquipmentService_GetEquipment0_HTTP_Handler(srv))
	r.GET("/api/v1/equipment", _EquipmentService_ListEquipment0_HTTP_Handler(srv))
	r.PUT("/api/v1/equipment/{id}", _EquipmentService_UpdateEquipment0_HTTP_Handler(srv))
	r.DELETE("/api/v1/equipment/{id}", _EquipmentService_DeleteEquipment0_HTTP_Handler(srv))
	r.GET("/api/v1/equipment/{id}/health", _EquipmentService_GetEquipmentHealth0_HTTP_Handler(srv))
	r.POST("/api/v1/equipment/{id}/health", _EquipmentService_UpdateEquipmentHealth0_HTTP_Handler(srv))
	r.POST("/api/v1/equipment/{id}/maintenance/schedule", _EquipmentService_ScheduleMaintenance0_HTTP_Handler(srv))
	r.GET("/api/v1/equipment/{id}/maintenance/history", _EquipmentService_GetMaintenanceHistory0_HTTP_Handler(srv))
	r.GET("/api/v1/equipment/{id}/parts", _EquipmentService_GetEquipmentParts0_HTTP_Handler(srv))
	r.PUT("/api/v1/equipment/{id}/parts/{part_id}", _EquipmentService_UpdatePartStatus0_HTTP_Handler(srv))
	r.GET("/api/v1/equipment/{id}/analytics", _EquipmentService_GetEquipmentAnalytics0_HTTP_Handler(srv))
	r.GET("/api/v1/equipment/fleet/overview", _EquipmentService_GetFleetOverview0_HTTP_Handler(srv))
	r.POST("/api/v1/equipment/{id}/qr-code", _EquipmentService_GenerateQRCode0_HTTP_Handler(srv))
}

func _EquipmentService_CreateEquipment0_HTTP_Handler(srv EquipmentServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateEquipmentRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationEquipmentServiceCreateEquipment)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateEquipment(ctx, req.(*CreateEquipmentRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateEquipmentResponse)
		return ctx.Result(200, reply)
	}
}

func _EquipmentService_GetEquipment0_HTTP_Handler(srv EquipmentServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetEquipmentRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationEquipmentServiceGetEquipment)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetEquipment(ctx, req.(*GetEquipmentRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetEquipmentResponse)
		return ctx.Result(200, reply)
	}
}

func _EquipmentService_ListEquipment0_HTTP_Handler(srv EquipmentServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListEquipmentRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationEquipmentServiceListEquipment)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListEquipment(ctx, req.(*ListEquipmentRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListEquipmentResponse)
		return ctx.Result(200, reply)
	}
}

func _EquipmentService_UpdateEquipment0_HTTP_Handler(srv EquipmentServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateEquipmentRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationEquipmentServiceUpdateEquipment)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateEquipment(ctx, req.(*UpdateEquipmentRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UpdateEquipmentResponse)
		return ctx.Result(200, reply)
	}
}

func _EquipmentService_DeleteEquipment0_HTTP_Handler(srv EquipmentServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteEquipmentRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationEquipmentServiceDeleteEquipment)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteEquipment(ctx, req.(*DeleteEquipmentRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DeleteEquipmentResponse)
		return ctx.Result(200, reply)
	}
}

func _EquipmentService_GetEquipmentHealth0_HTTP_Handler(srv EquipmentServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetEquipmentHealthRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationEquipmentServiceGetEquipmentHealth)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetEquipmentHealth(ctx, req.(*GetEquipmentHealthRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetEquipmentHealthResponse)
		return ctx.Result(200, reply)
	}
}

func _EquipmentService_UpdateEquipmentHealth0_HTTP_Handler(srv EquipmentServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateEquipmentHealthRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationEquipmentServiceUpdateEquipmentHealth)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateEquipmentHealth(ctx, req.(*UpdateEquipmentHealthRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UpdateEquipmentHealthResponse)
		return ctx.Result(200, reply)
	}
}

func _EquipmentService_ScheduleMaintenance0_HTTP_Handler(srv EquipmentServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ScheduleMaintenanceRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationEquipmentServiceScheduleMaintenance)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ScheduleMaintenance(ctx, req.(*ScheduleMaintenanceRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ScheduleMaintenanceResponse)
		return ctx.Result(200, reply)
	}
}

func _EquipmentService_GetMaintenanceHistory0_HTTP_Handler(srv EquipmentServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetMaintenanceHistoryRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationEquipmentServiceGetMaintenanceHistory)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetMaintenanceHistory(ctx, req.(*GetMaintenanceHistoryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetMaintenanceHistoryResponse)
		return ctx.Result(200, reply)
	}
}

func _EquipmentService_GetEquipmentParts0_HTTP_Handler(srv EquipmentServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetEquipmentPartsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationEquipmentServiceGetEquipmentParts)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetEquipmentParts(ctx, req.(*GetEquipmentPartsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetEquipmentPartsResponse)
		return ctx.Result(200, reply)
	}
}

func _EquipmentService_UpdatePartStatus0_HTTP_Handler(srv EquipmentServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdatePartStatusRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationEquipmentServiceUpdatePartStatus)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdatePartStatus(ctx, req.(*UpdatePartStatusRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UpdatePartStatusResponse)
		return ctx.Result(200, reply)
	}
}

func _EquipmentService_GetEquipmentAnalytics0_HTTP_Handler(srv EquipmentServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetEquipmentAnalyticsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationEquipmentServiceGetEquipmentAnalytics)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetEquipmentAnalytics(ctx, req.(*GetEquipmentAnalyticsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetEquipmentAnalyticsResponse)
		return ctx.Result(200, reply)
	}
}

func _EquipmentService_GetFleetOverview0_HTTP_Handler(srv EquipmentServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetFleetOverviewRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationEquipmentServiceGetFleetOverview)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetFleetOverview(ctx, req.(*GetFleetOverviewRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetFleetOverviewResponse)
		return ctx.Result(200, reply)
	}
}

func _EquipmentService_GenerateQRCode0_HTTP_Handler(srv EquipmentServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GenerateQRCodeRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationEquipmentServiceGenerateQRCode)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GenerateQRCode(ctx, req.(*GenerateQRCodeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GenerateQRCodeResponse)
		return ctx.Result(200, reply)
	}
}

type EquipmentServiceHTTPClient interface {
	CreateEquipment(ctx context.Context, req *CreateEquipmentRequest, opts ...http.CallOption) (rsp *CreateEquipmentResponse, err error)
	DeleteEquipment(ctx context.Context, req *DeleteEquipmentRequest, opts ...http.CallOption) (rsp *DeleteEquipmentResponse, err error)
	GenerateQRCode(ctx context.Context, req *GenerateQRCodeRequest, opts ...http.CallOption) (rsp *GenerateQRCodeResponse, err error)
	GetEquipment(ctx context.Context, req *GetEquipmentRequest, opts ...http.CallOption) (rsp *GetEquipmentResponse, err error)
	GetEquipmentAnalytics(ctx context.Context, req *GetEquipmentAnalyticsRequest, opts ...http.CallOption) (rsp *GetEquipmentAnalyticsResponse, err error)
	GetEquipmentHealth(ctx context.Context, req *GetEquipmentHealthRequest, opts ...http.CallOption) (rsp *GetEquipmentHealthResponse, err error)
	GetEquipmentParts(ctx context.Context, req *GetEquipmentPartsRequest, opts ...http.CallOption) (rsp *GetEquipmentPartsResponse, err error)
	GetFleetOverview(ctx context.Context, req *GetFleetOverviewRequest, opts ...http.CallOption) (rsp *GetFleetOverviewResponse, err error)
	GetMaintenanceHistory(ctx context.Context, req *GetMaintenanceHistoryRequest, opts ...http.CallOption) (rsp *GetMaintenanceHistoryResponse, err error)
	ListEquipment(ctx context.Context, req *ListEquipmentRequest, opts ...http.CallOption) (rsp *ListEquipmentResponse, err error)
	ScheduleMaintenance(ctx context.Context, req *ScheduleMaintenanceRequest, opts ...http.CallOption) (rsp *ScheduleMaintenanceResponse, err error)
	UpdateEquipment(ctx context.Context, req *UpdateEquipmentRequest, opts ...http.CallOption) (rsp *UpdateEquipmentResponse, err error)
	UpdateEquipmentHealth(ctx context.Context, req *UpdateEquipmentHealthRequest, opts ...http.CallOption) (rsp *UpdateEquipmentHealthResponse, err error)
	UpdatePartStatus(ctx context.Context, req *UpdatePartStatusRequest, opts ...http.CallOption) (rsp *UpdatePartStatusResponse, err error)
}

type EquipmentServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewEquipmentServiceHTTPClient(client *http.Client) EquipmentServiceHTTPClient {
	return &EquipmentServiceHTTPClientImpl{client}
}

func (c *EquipmentServiceHTTPClientImpl) CreateEquipment(ctx context.Context, in *CreateEquipmentRequest, opts ...http.CallOption) (*CreateEquipmentResponse, error) {
	var out CreateEquipmentResponse
	pattern := "/api/v1/equipment"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationEquipmentServiceCreateEquipment))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *EquipmentServiceHTTPClientImpl) DeleteEquipment(ctx context.Context, in *DeleteEquipmentRequest, opts ...http.CallOption) (*DeleteEquipmentResponse, error) {
	var out DeleteEquipmentResponse
	pattern := "/api/v1/equipment/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationEquipmentServiceDeleteEquipment))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *EquipmentServiceHTTPClientImpl) GenerateQRCode(ctx context.Context, in *GenerateQRCodeRequest, opts ...http.CallOption) (*GenerateQRCodeResponse, error) {
	var out GenerateQRCodeResponse
	pattern := "/api/v1/equipment/{id}/qr-code"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationEquipmentServiceGenerateQRCode))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *EquipmentServiceHTTPClientImpl) GetEquipment(ctx context.Context, in *GetEquipmentRequest, opts ...http.CallOption) (*GetEquipmentResponse, error) {
	var out GetEquipmentResponse
	pattern := "/api/v1/equipment/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationEquipmentServiceGetEquipment))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *EquipmentServiceHTTPClientImpl) GetEquipmentAnalytics(ctx context.Context, in *GetEquipmentAnalyticsRequest, opts ...http.CallOption) (*GetEquipmentAnalyticsResponse, error) {
	var out GetEquipmentAnalyticsResponse
	pattern := "/api/v1/equipment/{id}/analytics"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationEquipmentServiceGetEquipmentAnalytics))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *EquipmentServiceHTTPClientImpl) GetEquipmentHealth(ctx context.Context, in *GetEquipmentHealthRequest, opts ...http.CallOption) (*GetEquipmentHealthResponse, error) {
	var out GetEquipmentHealthResponse
	pattern := "/api/v1/equipment/{id}/health"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationEquipmentServiceGetEquipmentHealth))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *EquipmentServiceHTTPClientImpl) GetEquipmentParts(ctx context.Context, in *GetEquipmentPartsRequest, opts ...http.CallOption) (*GetEquipmentPartsResponse, error) {
	var out GetEquipmentPartsResponse
	pattern := "/api/v1/equipment/{id}/parts"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationEquipmentServiceGetEquipmentParts))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *EquipmentServiceHTTPClientImpl) GetFleetOverview(ctx context.Context, in *GetFleetOverviewRequest, opts ...http.CallOption) (*GetFleetOverviewResponse, error) {
	var out GetFleetOverviewResponse
	pattern := "/api/v1/equipment/fleet/overview"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationEquipmentServiceGetFleetOverview))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *EquipmentServiceHTTPClientImpl) GetMaintenanceHistory(ctx context.Context, in *GetMaintenanceHistoryRequest, opts ...http.CallOption) (*GetMaintenanceHistoryResponse, error) {
	var out GetMaintenanceHistoryResponse
	pattern := "/api/v1/equipment/{id}/maintenance/history"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationEquipmentServiceGetMaintenanceHistory))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *EquipmentServiceHTTPClientImpl) ListEquipment(ctx context.Context, in *ListEquipmentRequest, opts ...http.CallOption) (*ListEquipmentResponse, error) {
	var out ListEquipmentResponse
	pattern := "/api/v1/equipment"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationEquipmentServiceListEquipment))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *EquipmentServiceHTTPClientImpl) ScheduleMaintenance(ctx context.Context, in *ScheduleMaintenanceRequest, opts ...http.CallOption) (*ScheduleMaintenanceResponse, error) {
	var out ScheduleMaintenanceResponse
	pattern := "/api/v1/equipment/{id}/maintenance/schedule"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationEquipmentServiceScheduleMaintenance))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *EquipmentServiceHTTPClientImpl) UpdateEquipment(ctx context.Context, in *UpdateEquipmentRequest, opts ...http.CallOption) (*UpdateEquipmentResponse, error) {
	var out UpdateEquipmentResponse
	pattern := "/api/v1/equipment/{id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationEquipmentServiceUpdateEquipment))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *EquipmentServiceHTTPClientImpl) UpdateEquipmentHealth(ctx context.Context, in *UpdateEquipmentHealthRequest, opts ...http.CallOption) (*UpdateEquipmentHealthResponse, error) {
	var out UpdateEquipmentHealthResponse
	pattern := "/api/v1/equipment/{id}/health"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationEquipmentServiceUpdateEquipmentHealth))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *EquipmentServiceHTTPClientImpl) UpdatePartStatus(ctx context.Context, in *UpdatePartStatusRequest, opts ...http.CallOption) (*UpdatePartStatusResponse, error) {
	var out UpdatePartStatusResponse
	pattern := "/api/v1/equipment/{id}/parts/{part_id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationEquipmentServiceUpdatePartStatus))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
