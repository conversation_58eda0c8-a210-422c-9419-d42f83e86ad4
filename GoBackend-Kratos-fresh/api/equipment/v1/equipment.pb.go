// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: equipment/v1/equipment.proto

package v1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EquipmentStatus int32

const (
	EquipmentStatus_EQUIPMENT_STATUS_UNSPECIFIED EquipmentStatus = 0
	EquipmentStatus_EQUIPMENT_STATUS_ACTIVE      EquipmentStatus = 1
	EquipmentStatus_EQUIPMENT_STATUS_INACTIVE    EquipmentStatus = 2
	EquipmentStatus_EQUIPMENT_STATUS_MAINTENANCE EquipmentStatus = 3
	EquipmentStatus_EQUIPMENT_STATUS_RETIRED     EquipmentStatus = 4
	EquipmentStatus_EQUIPMENT_STATUS_FAULTY      EquipmentStatus = 5
)

// Enum value maps for EquipmentStatus.
var (
	EquipmentStatus_name = map[int32]string{
		0: "EQUIPMENT_STATUS_UNSPECIFIED",
		1: "EQUIPMENT_STATUS_ACTIVE",
		2: "EQUIPMENT_STATUS_INACTIVE",
		3: "EQUIPMENT_STATUS_MAINTENANCE",
		4: "EQUIPMENT_STATUS_RETIRED",
		5: "EQUIPMENT_STATUS_FAULTY",
	}
	EquipmentStatus_value = map[string]int32{
		"EQUIPMENT_STATUS_UNSPECIFIED": 0,
		"EQUIPMENT_STATUS_ACTIVE":      1,
		"EQUIPMENT_STATUS_INACTIVE":    2,
		"EQUIPMENT_STATUS_MAINTENANCE": 3,
		"EQUIPMENT_STATUS_RETIRED":     4,
		"EQUIPMENT_STATUS_FAULTY":      5,
	}
)

func (x EquipmentStatus) Enum() *EquipmentStatus {
	p := new(EquipmentStatus)
	*p = x
	return p
}

func (x EquipmentStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EquipmentStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_equipment_v1_equipment_proto_enumTypes[0].Descriptor()
}

func (EquipmentStatus) Type() protoreflect.EnumType {
	return &file_equipment_v1_equipment_proto_enumTypes[0]
}

func (x EquipmentStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EquipmentStatus.Descriptor instead.
func (EquipmentStatus) EnumDescriptor() ([]byte, []int) {
	return file_equipment_v1_equipment_proto_rawDescGZIP(), []int{0}
}

type Equipment struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Id               int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CustomerId       int64                  `protobuf:"varint,2,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	Name             string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Type             string                 `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"` // hvac_unit, heat_pump, air_conditioner, furnace, etc.
	Brand            string                 `protobuf:"bytes,5,opt,name=brand,proto3" json:"brand,omitempty"`
	Model            string                 `protobuf:"bytes,6,opt,name=model,proto3" json:"model,omitempty"`
	SerialNumber     string                 `protobuf:"bytes,7,opt,name=serial_number,json=serialNumber,proto3" json:"serial_number,omitempty"`
	InstallationDate *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=installation_date,json=installationDate,proto3" json:"installation_date,omitempty"`
	WarrantyExpiry   *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=warranty_expiry,json=warrantyExpiry,proto3" json:"warranty_expiry,omitempty"`
	Location         string                 `protobuf:"bytes,10,opt,name=location,proto3" json:"location,omitempty"` // Physical location description
	Latitude         float64                `protobuf:"fixed64,11,opt,name=latitude,proto3" json:"latitude,omitempty"`
	Longitude        float64                `protobuf:"fixed64,12,opt,name=longitude,proto3" json:"longitude,omitempty"`
	Status           EquipmentStatus        `protobuf:"varint,13,opt,name=status,proto3,enum=api.equipment.v1.EquipmentStatus" json:"status,omitempty"`
	Health           *EquipmentHealth       `protobuf:"bytes,14,opt,name=health,proto3" json:"health,omitempty"`
	Specifications   *structpb.Struct       `protobuf:"bytes,15,opt,name=specifications,proto3" json:"specifications,omitempty"` // Technical specifications
	Metadata         *structpb.Struct       `protobuf:"bytes,16,opt,name=metadata,proto3" json:"metadata,omitempty"`             // Additional metadata
	CreatedAt        *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt        *timestamppb.Timestamp `protobuf:"bytes,18,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *Equipment) Reset() {
	*x = Equipment{}
	mi := &file_equipment_v1_equipment_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Equipment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Equipment) ProtoMessage() {}

func (x *Equipment) ProtoReflect() protoreflect.Message {
	mi := &file_equipment_v1_equipment_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Equipment.ProtoReflect.Descriptor instead.
func (*Equipment) Descriptor() ([]byte, []int) {
	return file_equipment_v1_equipment_proto_rawDescGZIP(), []int{0}
}

func (x *Equipment) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Equipment) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *Equipment) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Equipment) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Equipment) GetBrand() string {
	if x != nil {
		return x.Brand
	}
	return ""
}

func (x *Equipment) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *Equipment) GetSerialNumber() string {
	if x != nil {
		return x.SerialNumber
	}
	return ""
}

func (x *Equipment) GetInstallationDate() *timestamppb.Timestamp {
	if x != nil {
		return x.InstallationDate
	}
	return nil
}

func (x *Equipment) GetWarrantyExpiry() *timestamppb.Timestamp {
	if x != nil {
		return x.WarrantyExpiry
	}
	return nil
}

func (x *Equipment) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *Equipment) GetLatitude() float64 {
	if x != nil {
		return x.Latitude
	}
	return 0
}

func (x *Equipment) GetLongitude() float64 {
	if x != nil {
		return x.Longitude
	}
	return 0
}

func (x *Equipment) GetStatus() EquipmentStatus {
	if x != nil {
		return x.Status
	}
	return EquipmentStatus_EQUIPMENT_STATUS_UNSPECIFIED
}

func (x *Equipment) GetHealth() *EquipmentHealth {
	if x != nil {
		return x.Health
	}
	return nil
}

func (x *Equipment) GetSpecifications() *structpb.Struct {
	if x != nil {
		return x.Specifications
	}
	return nil
}

func (x *Equipment) GetMetadata() *structpb.Struct {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *Equipment) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Equipment) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type EquipmentHealth struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	HealthScore      float64                `protobuf:"fixed64,1,opt,name=health_score,json=healthScore,proto3" json:"health_score,omitempty"`  // 0.0 to 1.0
	HealthStatus     string                 `protobuf:"bytes,2,opt,name=health_status,json=healthStatus,proto3" json:"health_status,omitempty"` // excellent, good, fair, poor, critical
	Metrics          []*HealthMetric        `protobuf:"bytes,3,rep,name=metrics,proto3" json:"metrics,omitempty"`
	LastAssessment   *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=last_assessment,json=lastAssessment,proto3" json:"last_assessment,omitempty"`
	AssessmentMethod string                 `protobuf:"bytes,5,opt,name=assessment_method,json=assessmentMethod,proto3" json:"assessment_method,omitempty"` // manual, iot_sensors, predictive_model
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *EquipmentHealth) Reset() {
	*x = EquipmentHealth{}
	mi := &file_equipment_v1_equipment_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EquipmentHealth) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EquipmentHealth) ProtoMessage() {}

func (x *EquipmentHealth) ProtoReflect() protoreflect.Message {
	mi := &file_equipment_v1_equipment_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EquipmentHealth.ProtoReflect.Descriptor instead.
func (*EquipmentHealth) Descriptor() ([]byte, []int) {
	return file_equipment_v1_equipment_proto_rawDescGZIP(), []int{1}
}

func (x *EquipmentHealth) GetHealthScore() float64 {
	if x != nil {
		return x.HealthScore
	}
	return 0
}

func (x *EquipmentHealth) GetHealthStatus() string {
	if x != nil {
		return x.HealthStatus
	}
	return ""
}

func (x *EquipmentHealth) GetMetrics() []*HealthMetric {
	if x != nil {
		return x.Metrics
	}
	return nil
}

func (x *EquipmentHealth) GetLastAssessment() *timestamppb.Timestamp {
	if x != nil {
		return x.LastAssessment
	}
	return nil
}

func (x *EquipmentHealth) GetAssessmentMethod() string {
	if x != nil {
		return x.AssessmentMethod
	}
	return ""
}

type HealthMetric struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Value         float64                `protobuf:"fixed64,2,opt,name=value,proto3" json:"value,omitempty"`
	Unit          string                 `protobuf:"bytes,3,opt,name=unit,proto3" json:"unit,omitempty"`
	ThresholdMin  float64                `protobuf:"fixed64,4,opt,name=threshold_min,json=thresholdMin,proto3" json:"threshold_min,omitempty"`
	ThresholdMax  float64                `protobuf:"fixed64,5,opt,name=threshold_max,json=thresholdMax,proto3" json:"threshold_max,omitempty"`
	Status        string                 `protobuf:"bytes,6,opt,name=status,proto3" json:"status,omitempty"` // normal, warning, critical
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthMetric) Reset() {
	*x = HealthMetric{}
	mi := &file_equipment_v1_equipment_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthMetric) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthMetric) ProtoMessage() {}

func (x *HealthMetric) ProtoReflect() protoreflect.Message {
	mi := &file_equipment_v1_equipment_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthMetric.ProtoReflect.Descriptor instead.
func (*HealthMetric) Descriptor() ([]byte, []int) {
	return file_equipment_v1_equipment_proto_rawDescGZIP(), []int{2}
}

func (x *HealthMetric) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *HealthMetric) GetValue() float64 {
	if x != nil {
		return x.Value
	}
	return 0
}

func (x *HealthMetric) GetUnit() string {
	if x != nil {
		return x.Unit
	}
	return ""
}

func (x *HealthMetric) GetThresholdMin() float64 {
	if x != nil {
		return x.ThresholdMin
	}
	return 0
}

func (x *HealthMetric) GetThresholdMax() float64 {
	if x != nil {
		return x.ThresholdMax
	}
	return 0
}

func (x *HealthMetric) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

// Equipment CRUD
type CreateEquipmentRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	CustomerId       int64                  `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	Name             string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Type             string                 `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	Brand            string                 `protobuf:"bytes,4,opt,name=brand,proto3" json:"brand,omitempty"`
	Model            string                 `protobuf:"bytes,5,opt,name=model,proto3" json:"model,omitempty"`
	SerialNumber     string                 `protobuf:"bytes,6,opt,name=serial_number,json=serialNumber,proto3" json:"serial_number,omitempty"`
	InstallationDate *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=installation_date,json=installationDate,proto3" json:"installation_date,omitempty"`
	WarrantyExpiry   *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=warranty_expiry,json=warrantyExpiry,proto3" json:"warranty_expiry,omitempty"`
	Location         string                 `protobuf:"bytes,9,opt,name=location,proto3" json:"location,omitempty"`
	Latitude         float64                `protobuf:"fixed64,10,opt,name=latitude,proto3" json:"latitude,omitempty"`
	Longitude        float64                `protobuf:"fixed64,11,opt,name=longitude,proto3" json:"longitude,omitempty"`
	Specifications   *structpb.Struct       `protobuf:"bytes,12,opt,name=specifications,proto3" json:"specifications,omitempty"`
	Metadata         *structpb.Struct       `protobuf:"bytes,13,opt,name=metadata,proto3" json:"metadata,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *CreateEquipmentRequest) Reset() {
	*x = CreateEquipmentRequest{}
	mi := &file_equipment_v1_equipment_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateEquipmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateEquipmentRequest) ProtoMessage() {}

func (x *CreateEquipmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_equipment_v1_equipment_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateEquipmentRequest.ProtoReflect.Descriptor instead.
func (*CreateEquipmentRequest) Descriptor() ([]byte, []int) {
	return file_equipment_v1_equipment_proto_rawDescGZIP(), []int{3}
}

func (x *CreateEquipmentRequest) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *CreateEquipmentRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateEquipmentRequest) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *CreateEquipmentRequest) GetBrand() string {
	if x != nil {
		return x.Brand
	}
	return ""
}

func (x *CreateEquipmentRequest) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *CreateEquipmentRequest) GetSerialNumber() string {
	if x != nil {
		return x.SerialNumber
	}
	return ""
}

func (x *CreateEquipmentRequest) GetInstallationDate() *timestamppb.Timestamp {
	if x != nil {
		return x.InstallationDate
	}
	return nil
}

func (x *CreateEquipmentRequest) GetWarrantyExpiry() *timestamppb.Timestamp {
	if x != nil {
		return x.WarrantyExpiry
	}
	return nil
}

func (x *CreateEquipmentRequest) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *CreateEquipmentRequest) GetLatitude() float64 {
	if x != nil {
		return x.Latitude
	}
	return 0
}

func (x *CreateEquipmentRequest) GetLongitude() float64 {
	if x != nil {
		return x.Longitude
	}
	return 0
}

func (x *CreateEquipmentRequest) GetSpecifications() *structpb.Struct {
	if x != nil {
		return x.Specifications
	}
	return nil
}

func (x *CreateEquipmentRequest) GetMetadata() *structpb.Struct {
	if x != nil {
		return x.Metadata
	}
	return nil
}

type CreateEquipmentResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Equipment     *Equipment             `protobuf:"bytes,1,opt,name=equipment,proto3" json:"equipment,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateEquipmentResponse) Reset() {
	*x = CreateEquipmentResponse{}
	mi := &file_equipment_v1_equipment_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateEquipmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateEquipmentResponse) ProtoMessage() {}

func (x *CreateEquipmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_equipment_v1_equipment_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateEquipmentResponse.ProtoReflect.Descriptor instead.
func (*CreateEquipmentResponse) Descriptor() ([]byte, []int) {
	return file_equipment_v1_equipment_proto_rawDescGZIP(), []int{4}
}

func (x *CreateEquipmentResponse) GetEquipment() *Equipment {
	if x != nil {
		return x.Equipment
	}
	return nil
}

type GetEquipmentRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetEquipmentRequest) Reset() {
	*x = GetEquipmentRequest{}
	mi := &file_equipment_v1_equipment_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetEquipmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEquipmentRequest) ProtoMessage() {}

func (x *GetEquipmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_equipment_v1_equipment_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEquipmentRequest.ProtoReflect.Descriptor instead.
func (*GetEquipmentRequest) Descriptor() ([]byte, []int) {
	return file_equipment_v1_equipment_proto_rawDescGZIP(), []int{5}
}

func (x *GetEquipmentRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetEquipmentResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Equipment     *Equipment             `protobuf:"bytes,1,opt,name=equipment,proto3" json:"equipment,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetEquipmentResponse) Reset() {
	*x = GetEquipmentResponse{}
	mi := &file_equipment_v1_equipment_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetEquipmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEquipmentResponse) ProtoMessage() {}

func (x *GetEquipmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_equipment_v1_equipment_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEquipmentResponse.ProtoReflect.Descriptor instead.
func (*GetEquipmentResponse) Descriptor() ([]byte, []int) {
	return file_equipment_v1_equipment_proto_rawDescGZIP(), []int{6}
}

func (x *GetEquipmentResponse) GetEquipment() *Equipment {
	if x != nil {
		return x.Equipment
	}
	return nil
}

type ListEquipmentRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	CustomerId    int64                  `protobuf:"varint,3,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	Type          string                 `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`
	Status        string                 `protobuf:"bytes,5,opt,name=status,proto3" json:"status,omitempty"`
	HealthStatus  string                 `protobuf:"bytes,6,opt,name=health_status,json=healthStatus,proto3" json:"health_status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListEquipmentRequest) Reset() {
	*x = ListEquipmentRequest{}
	mi := &file_equipment_v1_equipment_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListEquipmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListEquipmentRequest) ProtoMessage() {}

func (x *ListEquipmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_equipment_v1_equipment_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListEquipmentRequest.ProtoReflect.Descriptor instead.
func (*ListEquipmentRequest) Descriptor() ([]byte, []int) {
	return file_equipment_v1_equipment_proto_rawDescGZIP(), []int{7}
}

func (x *ListEquipmentRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListEquipmentRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListEquipmentRequest) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *ListEquipmentRequest) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *ListEquipmentRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *ListEquipmentRequest) GetHealthStatus() string {
	if x != nil {
		return x.HealthStatus
	}
	return ""
}

type ListEquipmentResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Equipment     []*Equipment           `protobuf:"bytes,1,rep,name=equipment,proto3" json:"equipment,omitempty"`
	TotalCount    int32                  `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	Page          int32                  `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListEquipmentResponse) Reset() {
	*x = ListEquipmentResponse{}
	mi := &file_equipment_v1_equipment_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListEquipmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListEquipmentResponse) ProtoMessage() {}

func (x *ListEquipmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_equipment_v1_equipment_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListEquipmentResponse.ProtoReflect.Descriptor instead.
func (*ListEquipmentResponse) Descriptor() ([]byte, []int) {
	return file_equipment_v1_equipment_proto_rawDescGZIP(), []int{8}
}

func (x *ListEquipmentResponse) GetEquipment() []*Equipment {
	if x != nil {
		return x.Equipment
	}
	return nil
}

func (x *ListEquipmentResponse) GetTotalCount() int32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *ListEquipmentResponse) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListEquipmentResponse) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type UpdateEquipmentRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Id             int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name           string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Location       string                 `protobuf:"bytes,3,opt,name=location,proto3" json:"location,omitempty"`
	Latitude       float64                `protobuf:"fixed64,4,opt,name=latitude,proto3" json:"latitude,omitempty"`
	Longitude      float64                `protobuf:"fixed64,5,opt,name=longitude,proto3" json:"longitude,omitempty"`
	Status         EquipmentStatus        `protobuf:"varint,6,opt,name=status,proto3,enum=api.equipment.v1.EquipmentStatus" json:"status,omitempty"`
	Specifications *structpb.Struct       `protobuf:"bytes,7,opt,name=specifications,proto3" json:"specifications,omitempty"`
	Metadata       *structpb.Struct       `protobuf:"bytes,8,opt,name=metadata,proto3" json:"metadata,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *UpdateEquipmentRequest) Reset() {
	*x = UpdateEquipmentRequest{}
	mi := &file_equipment_v1_equipment_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateEquipmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateEquipmentRequest) ProtoMessage() {}

func (x *UpdateEquipmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_equipment_v1_equipment_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateEquipmentRequest.ProtoReflect.Descriptor instead.
func (*UpdateEquipmentRequest) Descriptor() ([]byte, []int) {
	return file_equipment_v1_equipment_proto_rawDescGZIP(), []int{9}
}

func (x *UpdateEquipmentRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateEquipmentRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateEquipmentRequest) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *UpdateEquipmentRequest) GetLatitude() float64 {
	if x != nil {
		return x.Latitude
	}
	return 0
}

func (x *UpdateEquipmentRequest) GetLongitude() float64 {
	if x != nil {
		return x.Longitude
	}
	return 0
}

func (x *UpdateEquipmentRequest) GetStatus() EquipmentStatus {
	if x != nil {
		return x.Status
	}
	return EquipmentStatus_EQUIPMENT_STATUS_UNSPECIFIED
}

func (x *UpdateEquipmentRequest) GetSpecifications() *structpb.Struct {
	if x != nil {
		return x.Specifications
	}
	return nil
}

func (x *UpdateEquipmentRequest) GetMetadata() *structpb.Struct {
	if x != nil {
		return x.Metadata
	}
	return nil
}

type UpdateEquipmentResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Equipment     *Equipment             `protobuf:"bytes,1,opt,name=equipment,proto3" json:"equipment,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateEquipmentResponse) Reset() {
	*x = UpdateEquipmentResponse{}
	mi := &file_equipment_v1_equipment_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateEquipmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateEquipmentResponse) ProtoMessage() {}

func (x *UpdateEquipmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_equipment_v1_equipment_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateEquipmentResponse.ProtoReflect.Descriptor instead.
func (*UpdateEquipmentResponse) Descriptor() ([]byte, []int) {
	return file_equipment_v1_equipment_proto_rawDescGZIP(), []int{10}
}

func (x *UpdateEquipmentResponse) GetEquipment() *Equipment {
	if x != nil {
		return x.Equipment
	}
	return nil
}

type DeleteEquipmentRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteEquipmentRequest) Reset() {
	*x = DeleteEquipmentRequest{}
	mi := &file_equipment_v1_equipment_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteEquipmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteEquipmentRequest) ProtoMessage() {}

func (x *DeleteEquipmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_equipment_v1_equipment_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteEquipmentRequest.ProtoReflect.Descriptor instead.
func (*DeleteEquipmentRequest) Descriptor() ([]byte, []int) {
	return file_equipment_v1_equipment_proto_rawDescGZIP(), []int{11}
}

func (x *DeleteEquipmentRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type DeleteEquipmentResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteEquipmentResponse) Reset() {
	*x = DeleteEquipmentResponse{}
	mi := &file_equipment_v1_equipment_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteEquipmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteEquipmentResponse) ProtoMessage() {}

func (x *DeleteEquipmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_equipment_v1_equipment_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteEquipmentResponse.ProtoReflect.Descriptor instead.
func (*DeleteEquipmentResponse) Descriptor() ([]byte, []int) {
	return file_equipment_v1_equipment_proto_rawDescGZIP(), []int{12}
}

func (x *DeleteEquipmentResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// Health Monitoring
type GetEquipmentHealthRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetEquipmentHealthRequest) Reset() {
	*x = GetEquipmentHealthRequest{}
	mi := &file_equipment_v1_equipment_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetEquipmentHealthRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEquipmentHealthRequest) ProtoMessage() {}

func (x *GetEquipmentHealthRequest) ProtoReflect() protoreflect.Message {
	mi := &file_equipment_v1_equipment_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEquipmentHealthRequest.ProtoReflect.Descriptor instead.
func (*GetEquipmentHealthRequest) Descriptor() ([]byte, []int) {
	return file_equipment_v1_equipment_proto_rawDescGZIP(), []int{13}
}

func (x *GetEquipmentHealthRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetEquipmentHealthResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Health        *EquipmentHealth       `protobuf:"bytes,1,opt,name=health,proto3" json:"health,omitempty"`
	Trends        []*HealthTrend         `protobuf:"bytes,2,rep,name=trends,proto3" json:"trends,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetEquipmentHealthResponse) Reset() {
	*x = GetEquipmentHealthResponse{}
	mi := &file_equipment_v1_equipment_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetEquipmentHealthResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEquipmentHealthResponse) ProtoMessage() {}

func (x *GetEquipmentHealthResponse) ProtoReflect() protoreflect.Message {
	mi := &file_equipment_v1_equipment_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEquipmentHealthResponse.ProtoReflect.Descriptor instead.
func (*GetEquipmentHealthResponse) Descriptor() ([]byte, []int) {
	return file_equipment_v1_equipment_proto_rawDescGZIP(), []int{14}
}

func (x *GetEquipmentHealthResponse) GetHealth() *EquipmentHealth {
	if x != nil {
		return x.Health
	}
	return nil
}

func (x *GetEquipmentHealthResponse) GetTrends() []*HealthTrend {
	if x != nil {
		return x.Trends
	}
	return nil
}

type UpdateEquipmentHealthRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Id               int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Metrics          []*HealthMetric        `protobuf:"bytes,2,rep,name=metrics,proto3" json:"metrics,omitempty"`
	AssessmentMethod string                 `protobuf:"bytes,3,opt,name=assessment_method,json=assessmentMethod,proto3" json:"assessment_method,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *UpdateEquipmentHealthRequest) Reset() {
	*x = UpdateEquipmentHealthRequest{}
	mi := &file_equipment_v1_equipment_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateEquipmentHealthRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateEquipmentHealthRequest) ProtoMessage() {}

func (x *UpdateEquipmentHealthRequest) ProtoReflect() protoreflect.Message {
	mi := &file_equipment_v1_equipment_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateEquipmentHealthRequest.ProtoReflect.Descriptor instead.
func (*UpdateEquipmentHealthRequest) Descriptor() ([]byte, []int) {
	return file_equipment_v1_equipment_proto_rawDescGZIP(), []int{15}
}

func (x *UpdateEquipmentHealthRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateEquipmentHealthRequest) GetMetrics() []*HealthMetric {
	if x != nil {
		return x.Metrics
	}
	return nil
}

func (x *UpdateEquipmentHealthRequest) GetAssessmentMethod() string {
	if x != nil {
		return x.AssessmentMethod
	}
	return ""
}

type UpdateEquipmentHealthResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Health        *EquipmentHealth       `protobuf:"bytes,1,opt,name=health,proto3" json:"health,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateEquipmentHealthResponse) Reset() {
	*x = UpdateEquipmentHealthResponse{}
	mi := &file_equipment_v1_equipment_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateEquipmentHealthResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateEquipmentHealthResponse) ProtoMessage() {}

func (x *UpdateEquipmentHealthResponse) ProtoReflect() protoreflect.Message {
	mi := &file_equipment_v1_equipment_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateEquipmentHealthResponse.ProtoReflect.Descriptor instead.
func (*UpdateEquipmentHealthResponse) Descriptor() ([]byte, []int) {
	return file_equipment_v1_equipment_proto_rawDescGZIP(), []int{16}
}

func (x *UpdateEquipmentHealthResponse) GetHealth() *EquipmentHealth {
	if x != nil {
		return x.Health
	}
	return nil
}

type HealthTrend struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MetricName    string                 `protobuf:"bytes,1,opt,name=metric_name,json=metricName,proto3" json:"metric_name,omitempty"`
	DataPoints    []*HealthDataPoint     `protobuf:"bytes,2,rep,name=data_points,json=dataPoints,proto3" json:"data_points,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthTrend) Reset() {
	*x = HealthTrend{}
	mi := &file_equipment_v1_equipment_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthTrend) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthTrend) ProtoMessage() {}

func (x *HealthTrend) ProtoReflect() protoreflect.Message {
	mi := &file_equipment_v1_equipment_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthTrend.ProtoReflect.Descriptor instead.
func (*HealthTrend) Descriptor() ([]byte, []int) {
	return file_equipment_v1_equipment_proto_rawDescGZIP(), []int{17}
}

func (x *HealthTrend) GetMetricName() string {
	if x != nil {
		return x.MetricName
	}
	return ""
}

func (x *HealthTrend) GetDataPoints() []*HealthDataPoint {
	if x != nil {
		return x.DataPoints
	}
	return nil
}

type HealthDataPoint struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Timestamp     *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Value         float64                `protobuf:"fixed64,2,opt,name=value,proto3" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthDataPoint) Reset() {
	*x = HealthDataPoint{}
	mi := &file_equipment_v1_equipment_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthDataPoint) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthDataPoint) ProtoMessage() {}

func (x *HealthDataPoint) ProtoReflect() protoreflect.Message {
	mi := &file_equipment_v1_equipment_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthDataPoint.ProtoReflect.Descriptor instead.
func (*HealthDataPoint) Descriptor() ([]byte, []int) {
	return file_equipment_v1_equipment_proto_rawDescGZIP(), []int{18}
}

func (x *HealthDataPoint) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *HealthDataPoint) GetValue() float64 {
	if x != nil {
		return x.Value
	}
	return 0
}

// Maintenance Management
type ScheduleMaintenanceRequest struct {
	state                    protoimpl.MessageState `protogen:"open.v1"`
	Id                       int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	MaintenanceType          string                 `protobuf:"bytes,2,opt,name=maintenance_type,json=maintenanceType,proto3" json:"maintenance_type,omitempty"` // preventive, corrective, emergency
	ScheduledDate            *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=scheduled_date,json=scheduledDate,proto3" json:"scheduled_date,omitempty"`
	Description              string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	TechnicianId             int64                  `protobuf:"varint,5,opt,name=technician_id,json=technicianId,proto3" json:"technician_id,omitempty"`
	RequiredParts            []string               `protobuf:"bytes,6,rep,name=required_parts,json=requiredParts,proto3" json:"required_parts,omitempty"`
	EstimatedDurationMinutes int32                  `protobuf:"varint,7,opt,name=estimated_duration_minutes,json=estimatedDurationMinutes,proto3" json:"estimated_duration_minutes,omitempty"`
	unknownFields            protoimpl.UnknownFields
	sizeCache                protoimpl.SizeCache
}

func (x *ScheduleMaintenanceRequest) Reset() {
	*x = ScheduleMaintenanceRequest{}
	mi := &file_equipment_v1_equipment_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ScheduleMaintenanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScheduleMaintenanceRequest) ProtoMessage() {}

func (x *ScheduleMaintenanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_equipment_v1_equipment_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScheduleMaintenanceRequest.ProtoReflect.Descriptor instead.
func (*ScheduleMaintenanceRequest) Descriptor() ([]byte, []int) {
	return file_equipment_v1_equipment_proto_rawDescGZIP(), []int{19}
}

func (x *ScheduleMaintenanceRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ScheduleMaintenanceRequest) GetMaintenanceType() string {
	if x != nil {
		return x.MaintenanceType
	}
	return ""
}

func (x *ScheduleMaintenanceRequest) GetScheduledDate() *timestamppb.Timestamp {
	if x != nil {
		return x.ScheduledDate
	}
	return nil
}

func (x *ScheduleMaintenanceRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ScheduleMaintenanceRequest) GetTechnicianId() int64 {
	if x != nil {
		return x.TechnicianId
	}
	return 0
}

func (x *ScheduleMaintenanceRequest) GetRequiredParts() []string {
	if x != nil {
		return x.RequiredParts
	}
	return nil
}

func (x *ScheduleMaintenanceRequest) GetEstimatedDurationMinutes() int32 {
	if x != nil {
		return x.EstimatedDurationMinutes
	}
	return 0
}

type ScheduleMaintenanceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Schedule      *MaintenanceSchedule   `protobuf:"bytes,1,opt,name=schedule,proto3" json:"schedule,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ScheduleMaintenanceResponse) Reset() {
	*x = ScheduleMaintenanceResponse{}
	mi := &file_equipment_v1_equipment_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ScheduleMaintenanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScheduleMaintenanceResponse) ProtoMessage() {}

func (x *ScheduleMaintenanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_equipment_v1_equipment_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScheduleMaintenanceResponse.ProtoReflect.Descriptor instead.
func (*ScheduleMaintenanceResponse) Descriptor() ([]byte, []int) {
	return file_equipment_v1_equipment_proto_rawDescGZIP(), []int{20}
}

func (x *ScheduleMaintenanceResponse) GetSchedule() *MaintenanceSchedule {
	if x != nil {
		return x.Schedule
	}
	return nil
}

type GetMaintenanceHistoryRequest struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Id              int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Page            int32                  `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageSize        int32                  `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	MaintenanceType string                 `protobuf:"bytes,4,opt,name=maintenance_type,json=maintenanceType,proto3" json:"maintenance_type,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *GetMaintenanceHistoryRequest) Reset() {
	*x = GetMaintenanceHistoryRequest{}
	mi := &file_equipment_v1_equipment_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMaintenanceHistoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMaintenanceHistoryRequest) ProtoMessage() {}

func (x *GetMaintenanceHistoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_equipment_v1_equipment_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMaintenanceHistoryRequest.ProtoReflect.Descriptor instead.
func (*GetMaintenanceHistoryRequest) Descriptor() ([]byte, []int) {
	return file_equipment_v1_equipment_proto_rawDescGZIP(), []int{21}
}

func (x *GetMaintenanceHistoryRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetMaintenanceHistoryRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetMaintenanceHistoryRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetMaintenanceHistoryRequest) GetMaintenanceType() string {
	if x != nil {
		return x.MaintenanceType
	}
	return ""
}

type GetMaintenanceHistoryResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Records       []*MaintenanceRecord   `protobuf:"bytes,1,rep,name=records,proto3" json:"records,omitempty"`
	TotalCount    int32                  `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetMaintenanceHistoryResponse) Reset() {
	*x = GetMaintenanceHistoryResponse{}
	mi := &file_equipment_v1_equipment_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMaintenanceHistoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMaintenanceHistoryResponse) ProtoMessage() {}

func (x *GetMaintenanceHistoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_equipment_v1_equipment_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMaintenanceHistoryResponse.ProtoReflect.Descriptor instead.
func (*GetMaintenanceHistoryResponse) Descriptor() ([]byte, []int) {
	return file_equipment_v1_equipment_proto_rawDescGZIP(), []int{22}
}

func (x *GetMaintenanceHistoryResponse) GetRecords() []*MaintenanceRecord {
	if x != nil {
		return x.Records
	}
	return nil
}

func (x *GetMaintenanceHistoryResponse) GetTotalCount() int32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type MaintenanceSchedule struct {
	state                    protoimpl.MessageState `protogen:"open.v1"`
	Id                       int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	EquipmentId              int64                  `protobuf:"varint,2,opt,name=equipment_id,json=equipmentId,proto3" json:"equipment_id,omitempty"`
	MaintenanceType          string                 `protobuf:"bytes,3,opt,name=maintenance_type,json=maintenanceType,proto3" json:"maintenance_type,omitempty"`
	ScheduledDate            *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=scheduled_date,json=scheduledDate,proto3" json:"scheduled_date,omitempty"`
	Description              string                 `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	TechnicianId             int64                  `protobuf:"varint,6,opt,name=technician_id,json=technicianId,proto3" json:"technician_id,omitempty"`
	RequiredParts            []string               `protobuf:"bytes,7,rep,name=required_parts,json=requiredParts,proto3" json:"required_parts,omitempty"`
	EstimatedDurationMinutes int32                  `protobuf:"varint,8,opt,name=estimated_duration_minutes,json=estimatedDurationMinutes,proto3" json:"estimated_duration_minutes,omitempty"`
	Status                   string                 `protobuf:"bytes,9,opt,name=status,proto3" json:"status,omitempty"` // scheduled, in_progress, completed, cancelled
	CreatedAt                *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	unknownFields            protoimpl.UnknownFields
	sizeCache                protoimpl.SizeCache
}

func (x *MaintenanceSchedule) Reset() {
	*x = MaintenanceSchedule{}
	mi := &file_equipment_v1_equipment_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MaintenanceSchedule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaintenanceSchedule) ProtoMessage() {}

func (x *MaintenanceSchedule) ProtoReflect() protoreflect.Message {
	mi := &file_equipment_v1_equipment_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaintenanceSchedule.ProtoReflect.Descriptor instead.
func (*MaintenanceSchedule) Descriptor() ([]byte, []int) {
	return file_equipment_v1_equipment_proto_rawDescGZIP(), []int{23}
}

func (x *MaintenanceSchedule) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MaintenanceSchedule) GetEquipmentId() int64 {
	if x != nil {
		return x.EquipmentId
	}
	return 0
}

func (x *MaintenanceSchedule) GetMaintenanceType() string {
	if x != nil {
		return x.MaintenanceType
	}
	return ""
}

func (x *MaintenanceSchedule) GetScheduledDate() *timestamppb.Timestamp {
	if x != nil {
		return x.ScheduledDate
	}
	return nil
}

func (x *MaintenanceSchedule) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *MaintenanceSchedule) GetTechnicianId() int64 {
	if x != nil {
		return x.TechnicianId
	}
	return 0
}

func (x *MaintenanceSchedule) GetRequiredParts() []string {
	if x != nil {
		return x.RequiredParts
	}
	return nil
}

func (x *MaintenanceSchedule) GetEstimatedDurationMinutes() int32 {
	if x != nil {
		return x.EstimatedDurationMinutes
	}
	return 0
}

func (x *MaintenanceSchedule) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *MaintenanceSchedule) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type MaintenanceRecord struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	Id                    int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	EquipmentId           int64                  `protobuf:"varint,2,opt,name=equipment_id,json=equipmentId,proto3" json:"equipment_id,omitempty"`
	MaintenanceType       string                 `protobuf:"bytes,3,opt,name=maintenance_type,json=maintenanceType,proto3" json:"maintenance_type,omitempty"`
	PerformedDate         *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=performed_date,json=performedDate,proto3" json:"performed_date,omitempty"`
	Description           string                 `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	TechnicianId          int64                  `protobuf:"varint,6,opt,name=technician_id,json=technicianId,proto3" json:"technician_id,omitempty"`
	PartsUsed             []string               `protobuf:"bytes,7,rep,name=parts_used,json=partsUsed,proto3" json:"parts_used,omitempty"`
	ActualDurationMinutes int32                  `protobuf:"varint,8,opt,name=actual_duration_minutes,json=actualDurationMinutes,proto3" json:"actual_duration_minutes,omitempty"`
	Status                string                 `protobuf:"bytes,9,opt,name=status,proto3" json:"status,omitempty"`
	Notes                 string                 `protobuf:"bytes,10,opt,name=notes,proto3" json:"notes,omitempty"`
	Cost                  float64                `protobuf:"fixed64,11,opt,name=cost,proto3" json:"cost,omitempty"`
	CreatedAt             *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *MaintenanceRecord) Reset() {
	*x = MaintenanceRecord{}
	mi := &file_equipment_v1_equipment_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MaintenanceRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaintenanceRecord) ProtoMessage() {}

func (x *MaintenanceRecord) ProtoReflect() protoreflect.Message {
	mi := &file_equipment_v1_equipment_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaintenanceRecord.ProtoReflect.Descriptor instead.
func (*MaintenanceRecord) Descriptor() ([]byte, []int) {
	return file_equipment_v1_equipment_proto_rawDescGZIP(), []int{24}
}

func (x *MaintenanceRecord) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MaintenanceRecord) GetEquipmentId() int64 {
	if x != nil {
		return x.EquipmentId
	}
	return 0
}

func (x *MaintenanceRecord) GetMaintenanceType() string {
	if x != nil {
		return x.MaintenanceType
	}
	return ""
}

func (x *MaintenanceRecord) GetPerformedDate() *timestamppb.Timestamp {
	if x != nil {
		return x.PerformedDate
	}
	return nil
}

func (x *MaintenanceRecord) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *MaintenanceRecord) GetTechnicianId() int64 {
	if x != nil {
		return x.TechnicianId
	}
	return 0
}

func (x *MaintenanceRecord) GetPartsUsed() []string {
	if x != nil {
		return x.PartsUsed
	}
	return nil
}

func (x *MaintenanceRecord) GetActualDurationMinutes() int32 {
	if x != nil {
		return x.ActualDurationMinutes
	}
	return 0
}

func (x *MaintenanceRecord) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *MaintenanceRecord) GetNotes() string {
	if x != nil {
		return x.Notes
	}
	return ""
}

func (x *MaintenanceRecord) GetCost() float64 {
	if x != nil {
		return x.Cost
	}
	return 0
}

func (x *MaintenanceRecord) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

// Parts Management
type GetEquipmentPartsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetEquipmentPartsRequest) Reset() {
	*x = GetEquipmentPartsRequest{}
	mi := &file_equipment_v1_equipment_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetEquipmentPartsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEquipmentPartsRequest) ProtoMessage() {}

func (x *GetEquipmentPartsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_equipment_v1_equipment_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEquipmentPartsRequest.ProtoReflect.Descriptor instead.
func (*GetEquipmentPartsRequest) Descriptor() ([]byte, []int) {
	return file_equipment_v1_equipment_proto_rawDescGZIP(), []int{25}
}

func (x *GetEquipmentPartsRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetEquipmentPartsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Parts         []*EquipmentPart       `protobuf:"bytes,1,rep,name=parts,proto3" json:"parts,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetEquipmentPartsResponse) Reset() {
	*x = GetEquipmentPartsResponse{}
	mi := &file_equipment_v1_equipment_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetEquipmentPartsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEquipmentPartsResponse) ProtoMessage() {}

func (x *GetEquipmentPartsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_equipment_v1_equipment_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEquipmentPartsResponse.ProtoReflect.Descriptor instead.
func (*GetEquipmentPartsResponse) Descriptor() ([]byte, []int) {
	return file_equipment_v1_equipment_proto_rawDescGZIP(), []int{26}
}

func (x *GetEquipmentPartsResponse) GetParts() []*EquipmentPart {
	if x != nil {
		return x.Parts
	}
	return nil
}

type UpdatePartStatusRequest struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Id              int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	PartId          int64                  `protobuf:"varint,2,opt,name=part_id,json=partId,proto3" json:"part_id,omitempty"`
	Status          string                 `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"`
	ReplacementDate *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=replacement_date,json=replacementDate,proto3" json:"replacement_date,omitempty"`
	Notes           string                 `protobuf:"bytes,5,opt,name=notes,proto3" json:"notes,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *UpdatePartStatusRequest) Reset() {
	*x = UpdatePartStatusRequest{}
	mi := &file_equipment_v1_equipment_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdatePartStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePartStatusRequest) ProtoMessage() {}

func (x *UpdatePartStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_equipment_v1_equipment_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePartStatusRequest.ProtoReflect.Descriptor instead.
func (*UpdatePartStatusRequest) Descriptor() ([]byte, []int) {
	return file_equipment_v1_equipment_proto_rawDescGZIP(), []int{27}
}

func (x *UpdatePartStatusRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdatePartStatusRequest) GetPartId() int64 {
	if x != nil {
		return x.PartId
	}
	return 0
}

func (x *UpdatePartStatusRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *UpdatePartStatusRequest) GetReplacementDate() *timestamppb.Timestamp {
	if x != nil {
		return x.ReplacementDate
	}
	return nil
}

func (x *UpdatePartStatusRequest) GetNotes() string {
	if x != nil {
		return x.Notes
	}
	return ""
}

type UpdatePartStatusResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Part          *EquipmentPart         `protobuf:"bytes,1,opt,name=part,proto3" json:"part,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdatePartStatusResponse) Reset() {
	*x = UpdatePartStatusResponse{}
	mi := &file_equipment_v1_equipment_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdatePartStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePartStatusResponse) ProtoMessage() {}

func (x *UpdatePartStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_equipment_v1_equipment_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePartStatusResponse.ProtoReflect.Descriptor instead.
func (*UpdatePartStatusResponse) Descriptor() ([]byte, []int) {
	return file_equipment_v1_equipment_proto_rawDescGZIP(), []int{28}
}

func (x *UpdatePartStatusResponse) GetPart() *EquipmentPart {
	if x != nil {
		return x.Part
	}
	return nil
}

type EquipmentPart struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Id               int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	EquipmentId      int64                  `protobuf:"varint,2,opt,name=equipment_id,json=equipmentId,proto3" json:"equipment_id,omitempty"`
	Name             string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	PartNumber       string                 `protobuf:"bytes,4,opt,name=part_number,json=partNumber,proto3" json:"part_number,omitempty"`
	Manufacturer     string                 `protobuf:"bytes,5,opt,name=manufacturer,proto3" json:"manufacturer,omitempty"`
	InstallationDate *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=installation_date,json=installationDate,proto3" json:"installation_date,omitempty"`
	WarrantyExpiry   *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=warranty_expiry,json=warrantyExpiry,proto3" json:"warranty_expiry,omitempty"`
	Status           string                 `protobuf:"bytes,8,opt,name=status,proto3" json:"status,omitempty"` // new, good, worn, needs_replacement, replaced
	Cost             float64                `protobuf:"fixed64,9,opt,name=cost,proto3" json:"cost,omitempty"`
	Supplier         string                 `protobuf:"bytes,10,opt,name=supplier,proto3" json:"supplier,omitempty"`
	Specifications   *structpb.Struct       `protobuf:"bytes,11,opt,name=specifications,proto3" json:"specifications,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *EquipmentPart) Reset() {
	*x = EquipmentPart{}
	mi := &file_equipment_v1_equipment_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EquipmentPart) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EquipmentPart) ProtoMessage() {}

func (x *EquipmentPart) ProtoReflect() protoreflect.Message {
	mi := &file_equipment_v1_equipment_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EquipmentPart.ProtoReflect.Descriptor instead.
func (*EquipmentPart) Descriptor() ([]byte, []int) {
	return file_equipment_v1_equipment_proto_rawDescGZIP(), []int{29}
}

func (x *EquipmentPart) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *EquipmentPart) GetEquipmentId() int64 {
	if x != nil {
		return x.EquipmentId
	}
	return 0
}

func (x *EquipmentPart) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *EquipmentPart) GetPartNumber() string {
	if x != nil {
		return x.PartNumber
	}
	return ""
}

func (x *EquipmentPart) GetManufacturer() string {
	if x != nil {
		return x.Manufacturer
	}
	return ""
}

func (x *EquipmentPart) GetInstallationDate() *timestamppb.Timestamp {
	if x != nil {
		return x.InstallationDate
	}
	return nil
}

func (x *EquipmentPart) GetWarrantyExpiry() *timestamppb.Timestamp {
	if x != nil {
		return x.WarrantyExpiry
	}
	return nil
}

func (x *EquipmentPart) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *EquipmentPart) GetCost() float64 {
	if x != nil {
		return x.Cost
	}
	return 0
}

func (x *EquipmentPart) GetSupplier() string {
	if x != nil {
		return x.Supplier
	}
	return ""
}

func (x *EquipmentPart) GetSpecifications() *structpb.Struct {
	if x != nil {
		return x.Specifications
	}
	return nil
}

// Analytics and Reporting
type GetEquipmentAnalyticsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	StartDate     *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	EndDate       *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetEquipmentAnalyticsRequest) Reset() {
	*x = GetEquipmentAnalyticsRequest{}
	mi := &file_equipment_v1_equipment_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetEquipmentAnalyticsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEquipmentAnalyticsRequest) ProtoMessage() {}

func (x *GetEquipmentAnalyticsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_equipment_v1_equipment_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEquipmentAnalyticsRequest.ProtoReflect.Descriptor instead.
func (*GetEquipmentAnalyticsRequest) Descriptor() ([]byte, []int) {
	return file_equipment_v1_equipment_proto_rawDescGZIP(), []int{30}
}

func (x *GetEquipmentAnalyticsRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetEquipmentAnalyticsRequest) GetStartDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *GetEquipmentAnalyticsRequest) GetEndDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EndDate
	}
	return nil
}

type GetEquipmentAnalyticsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Analytics     *EquipmentAnalytics    `protobuf:"bytes,1,opt,name=analytics,proto3" json:"analytics,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetEquipmentAnalyticsResponse) Reset() {
	*x = GetEquipmentAnalyticsResponse{}
	mi := &file_equipment_v1_equipment_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetEquipmentAnalyticsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEquipmentAnalyticsResponse) ProtoMessage() {}

func (x *GetEquipmentAnalyticsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_equipment_v1_equipment_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEquipmentAnalyticsResponse.ProtoReflect.Descriptor instead.
func (*GetEquipmentAnalyticsResponse) Descriptor() ([]byte, []int) {
	return file_equipment_v1_equipment_proto_rawDescGZIP(), []int{31}
}

func (x *GetEquipmentAnalyticsResponse) GetAnalytics() *EquipmentAnalytics {
	if x != nil {
		return x.Analytics
	}
	return nil
}

type GetFleetOverviewRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CustomerId    int64                  `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`         // Optional: filter by customer
	EquipmentType string                 `protobuf:"bytes,2,opt,name=equipment_type,json=equipmentType,proto3" json:"equipment_type,omitempty"` // Optional: filter by type
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetFleetOverviewRequest) Reset() {
	*x = GetFleetOverviewRequest{}
	mi := &file_equipment_v1_equipment_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFleetOverviewRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFleetOverviewRequest) ProtoMessage() {}

func (x *GetFleetOverviewRequest) ProtoReflect() protoreflect.Message {
	mi := &file_equipment_v1_equipment_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFleetOverviewRequest.ProtoReflect.Descriptor instead.
func (*GetFleetOverviewRequest) Descriptor() ([]byte, []int) {
	return file_equipment_v1_equipment_proto_rawDescGZIP(), []int{32}
}

func (x *GetFleetOverviewRequest) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *GetFleetOverviewRequest) GetEquipmentType() string {
	if x != nil {
		return x.EquipmentType
	}
	return ""
}

type GetFleetOverviewResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Overview      *FleetOverview         `protobuf:"bytes,1,opt,name=overview,proto3" json:"overview,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetFleetOverviewResponse) Reset() {
	*x = GetFleetOverviewResponse{}
	mi := &file_equipment_v1_equipment_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFleetOverviewResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFleetOverviewResponse) ProtoMessage() {}

func (x *GetFleetOverviewResponse) ProtoReflect() protoreflect.Message {
	mi := &file_equipment_v1_equipment_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFleetOverviewResponse.ProtoReflect.Descriptor instead.
func (*GetFleetOverviewResponse) Descriptor() ([]byte, []int) {
	return file_equipment_v1_equipment_proto_rawDescGZIP(), []int{33}
}

func (x *GetFleetOverviewResponse) GetOverview() *FleetOverview {
	if x != nil {
		return x.Overview
	}
	return nil
}

type EquipmentAnalytics struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	EquipmentId           int64                  `protobuf:"varint,1,opt,name=equipment_id,json=equipmentId,proto3" json:"equipment_id,omitempty"`
	UptimePercentage      float64                `protobuf:"fixed64,2,opt,name=uptime_percentage,json=uptimePercentage,proto3" json:"uptime_percentage,omitempty"`
	MaintenanceCount      int32                  `protobuf:"varint,3,opt,name=maintenance_count,json=maintenanceCount,proto3" json:"maintenance_count,omitempty"`
	TotalMaintenanceCost  float64                `protobuf:"fixed64,4,opt,name=total_maintenance_cost,json=totalMaintenanceCost,proto3" json:"total_maintenance_cost,omitempty"`
	EnergyEfficiencyScore float64                `protobuf:"fixed64,5,opt,name=energy_efficiency_score,json=energyEfficiencyScore,proto3" json:"energy_efficiency_score,omitempty"`
	PerformanceMetrics    []*PerformanceMetric   `protobuf:"bytes,6,rep,name=performance_metrics,json=performanceMetrics,proto3" json:"performance_metrics,omitempty"`
	AnalysisPeriodStart   *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=analysis_period_start,json=analysisPeriodStart,proto3" json:"analysis_period_start,omitempty"`
	AnalysisPeriodEnd     *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=analysis_period_end,json=analysisPeriodEnd,proto3" json:"analysis_period_end,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *EquipmentAnalytics) Reset() {
	*x = EquipmentAnalytics{}
	mi := &file_equipment_v1_equipment_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EquipmentAnalytics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EquipmentAnalytics) ProtoMessage() {}

func (x *EquipmentAnalytics) ProtoReflect() protoreflect.Message {
	mi := &file_equipment_v1_equipment_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EquipmentAnalytics.ProtoReflect.Descriptor instead.
func (*EquipmentAnalytics) Descriptor() ([]byte, []int) {
	return file_equipment_v1_equipment_proto_rawDescGZIP(), []int{34}
}

func (x *EquipmentAnalytics) GetEquipmentId() int64 {
	if x != nil {
		return x.EquipmentId
	}
	return 0
}

func (x *EquipmentAnalytics) GetUptimePercentage() float64 {
	if x != nil {
		return x.UptimePercentage
	}
	return 0
}

func (x *EquipmentAnalytics) GetMaintenanceCount() int32 {
	if x != nil {
		return x.MaintenanceCount
	}
	return 0
}

func (x *EquipmentAnalytics) GetTotalMaintenanceCost() float64 {
	if x != nil {
		return x.TotalMaintenanceCost
	}
	return 0
}

func (x *EquipmentAnalytics) GetEnergyEfficiencyScore() float64 {
	if x != nil {
		return x.EnergyEfficiencyScore
	}
	return 0
}

func (x *EquipmentAnalytics) GetPerformanceMetrics() []*PerformanceMetric {
	if x != nil {
		return x.PerformanceMetrics
	}
	return nil
}

func (x *EquipmentAnalytics) GetAnalysisPeriodStart() *timestamppb.Timestamp {
	if x != nil {
		return x.AnalysisPeriodStart
	}
	return nil
}

func (x *EquipmentAnalytics) GetAnalysisPeriodEnd() *timestamppb.Timestamp {
	if x != nil {
		return x.AnalysisPeriodEnd
	}
	return nil
}

type FleetOverview struct {
	state              protoimpl.MessageState  `protogen:"open.v1"`
	TotalEquipment     int32                   `protobuf:"varint,1,opt,name=total_equipment,json=totalEquipment,proto3" json:"total_equipment,omitempty"`
	ActiveEquipment    int32                   `protobuf:"varint,2,opt,name=active_equipment,json=activeEquipment,proto3" json:"active_equipment,omitempty"`
	MaintenanceDue     int32                   `protobuf:"varint,3,opt,name=maintenance_due,json=maintenanceDue,proto3" json:"maintenance_due,omitempty"`
	CriticalHealth     int32                   `protobuf:"varint,4,opt,name=critical_health,json=criticalHealth,proto3" json:"critical_health,omitempty"`
	AverageHealthScore float64                 `protobuf:"fixed64,5,opt,name=average_health_score,json=averageHealthScore,proto3" json:"average_health_score,omitempty"`
	TotalFleetValue    float64                 `protobuf:"fixed64,6,opt,name=total_fleet_value,json=totalFleetValue,proto3" json:"total_fleet_value,omitempty"`
	TypeSummaries      []*EquipmentTypeSummary `protobuf:"bytes,7,rep,name=type_summaries,json=typeSummaries,proto3" json:"type_summaries,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *FleetOverview) Reset() {
	*x = FleetOverview{}
	mi := &file_equipment_v1_equipment_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FleetOverview) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FleetOverview) ProtoMessage() {}

func (x *FleetOverview) ProtoReflect() protoreflect.Message {
	mi := &file_equipment_v1_equipment_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FleetOverview.ProtoReflect.Descriptor instead.
func (*FleetOverview) Descriptor() ([]byte, []int) {
	return file_equipment_v1_equipment_proto_rawDescGZIP(), []int{35}
}

func (x *FleetOverview) GetTotalEquipment() int32 {
	if x != nil {
		return x.TotalEquipment
	}
	return 0
}

func (x *FleetOverview) GetActiveEquipment() int32 {
	if x != nil {
		return x.ActiveEquipment
	}
	return 0
}

func (x *FleetOverview) GetMaintenanceDue() int32 {
	if x != nil {
		return x.MaintenanceDue
	}
	return 0
}

func (x *FleetOverview) GetCriticalHealth() int32 {
	if x != nil {
		return x.CriticalHealth
	}
	return 0
}

func (x *FleetOverview) GetAverageHealthScore() float64 {
	if x != nil {
		return x.AverageHealthScore
	}
	return 0
}

func (x *FleetOverview) GetTotalFleetValue() float64 {
	if x != nil {
		return x.TotalFleetValue
	}
	return 0
}

func (x *FleetOverview) GetTypeSummaries() []*EquipmentTypeSummary {
	if x != nil {
		return x.TypeSummaries
	}
	return nil
}

type EquipmentTypeSummary struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	EquipmentType      string                 `protobuf:"bytes,1,opt,name=equipment_type,json=equipmentType,proto3" json:"equipment_type,omitempty"`
	Count              int32                  `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	AverageHealthScore float64                `protobuf:"fixed64,3,opt,name=average_health_score,json=averageHealthScore,proto3" json:"average_health_score,omitempty"`
	MaintenanceDue     int32                  `protobuf:"varint,4,opt,name=maintenance_due,json=maintenanceDue,proto3" json:"maintenance_due,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *EquipmentTypeSummary) Reset() {
	*x = EquipmentTypeSummary{}
	mi := &file_equipment_v1_equipment_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EquipmentTypeSummary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EquipmentTypeSummary) ProtoMessage() {}

func (x *EquipmentTypeSummary) ProtoReflect() protoreflect.Message {
	mi := &file_equipment_v1_equipment_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EquipmentTypeSummary.ProtoReflect.Descriptor instead.
func (*EquipmentTypeSummary) Descriptor() ([]byte, []int) {
	return file_equipment_v1_equipment_proto_rawDescGZIP(), []int{36}
}

func (x *EquipmentTypeSummary) GetEquipmentType() string {
	if x != nil {
		return x.EquipmentType
	}
	return ""
}

func (x *EquipmentTypeSummary) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *EquipmentTypeSummary) GetAverageHealthScore() float64 {
	if x != nil {
		return x.AverageHealthScore
	}
	return 0
}

func (x *EquipmentTypeSummary) GetMaintenanceDue() int32 {
	if x != nil {
		return x.MaintenanceDue
	}
	return 0
}

type PerformanceMetric struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Value         float64                `protobuf:"fixed64,2,opt,name=value,proto3" json:"value,omitempty"`
	Unit          string                 `protobuf:"bytes,3,opt,name=unit,proto3" json:"unit,omitempty"`
	Benchmark     float64                `protobuf:"fixed64,4,opt,name=benchmark,proto3" json:"benchmark,omitempty"`
	Trend         string                 `protobuf:"bytes,5,opt,name=trend,proto3" json:"trend,omitempty"` // improving, stable, declining
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PerformanceMetric) Reset() {
	*x = PerformanceMetric{}
	mi := &file_equipment_v1_equipment_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerformanceMetric) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerformanceMetric) ProtoMessage() {}

func (x *PerformanceMetric) ProtoReflect() protoreflect.Message {
	mi := &file_equipment_v1_equipment_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerformanceMetric.ProtoReflect.Descriptor instead.
func (*PerformanceMetric) Descriptor() ([]byte, []int) {
	return file_equipment_v1_equipment_proto_rawDescGZIP(), []int{37}
}

func (x *PerformanceMetric) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PerformanceMetric) GetValue() float64 {
	if x != nil {
		return x.Value
	}
	return 0
}

func (x *PerformanceMetric) GetUnit() string {
	if x != nil {
		return x.Unit
	}
	return ""
}

func (x *PerformanceMetric) GetBenchmark() float64 {
	if x != nil {
		return x.Benchmark
	}
	return 0
}

func (x *PerformanceMetric) GetTrend() string {
	if x != nil {
		return x.Trend
	}
	return ""
}

// QR Code Generation
type GenerateQRCodeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Format        string                 `protobuf:"bytes,2,opt,name=format,proto3" json:"format,omitempty"` // png, svg, pdf
	Size          int32                  `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"`    // Size in pixels
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GenerateQRCodeRequest) Reset() {
	*x = GenerateQRCodeRequest{}
	mi := &file_equipment_v1_equipment_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GenerateQRCodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateQRCodeRequest) ProtoMessage() {}

func (x *GenerateQRCodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_equipment_v1_equipment_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateQRCodeRequest.ProtoReflect.Descriptor instead.
func (*GenerateQRCodeRequest) Descriptor() ([]byte, []int) {
	return file_equipment_v1_equipment_proto_rawDescGZIP(), []int{38}
}

func (x *GenerateQRCodeRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GenerateQRCodeRequest) GetFormat() string {
	if x != nil {
		return x.Format
	}
	return ""
}

func (x *GenerateQRCodeRequest) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

type GenerateQRCodeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	QrCodeUrl     string                 `protobuf:"bytes,1,opt,name=qr_code_url,json=qrCodeUrl,proto3" json:"qr_code_url,omitempty"`
	QrCodeData    []byte                 `protobuf:"bytes,2,opt,name=qr_code_data,json=qrCodeData,proto3" json:"qr_code_data,omitempty"`
	Format        string                 `protobuf:"bytes,3,opt,name=format,proto3" json:"format,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GenerateQRCodeResponse) Reset() {
	*x = GenerateQRCodeResponse{}
	mi := &file_equipment_v1_equipment_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GenerateQRCodeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateQRCodeResponse) ProtoMessage() {}

func (x *GenerateQRCodeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_equipment_v1_equipment_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateQRCodeResponse.ProtoReflect.Descriptor instead.
func (*GenerateQRCodeResponse) Descriptor() ([]byte, []int) {
	return file_equipment_v1_equipment_proto_rawDescGZIP(), []int{39}
}

func (x *GenerateQRCodeResponse) GetQrCodeUrl() string {
	if x != nil {
		return x.QrCodeUrl
	}
	return ""
}

func (x *GenerateQRCodeResponse) GetQrCodeData() []byte {
	if x != nil {
		return x.QrCodeData
	}
	return nil
}

func (x *GenerateQRCodeResponse) GetFormat() string {
	if x != nil {
		return x.Format
	}
	return ""
}

var File_equipment_v1_equipment_proto protoreflect.FileDescriptor

const file_equipment_v1_equipment_proto_rawDesc = "" +
	"\n" +
	"\x1cequipment/v1/equipment.proto\x12\x10api.equipment.v1\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1cgoogle/protobuf/struct.proto\"\xfb\x05\n" +
	"\tEquipment\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n" +
	"\vcustomer_id\x18\x02 \x01(\x03R\n" +
	"customerId\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x12\n" +
	"\x04type\x18\x04 \x01(\tR\x04type\x12\x14\n" +
	"\x05brand\x18\x05 \x01(\tR\x05brand\x12\x14\n" +
	"\x05model\x18\x06 \x01(\tR\x05model\x12#\n" +
	"\rserial_number\x18\a \x01(\tR\fserialNumber\x12G\n" +
	"\x11installation_date\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\x10installationDate\x12C\n" +
	"\x0fwarranty_expiry\x18\t \x01(\v2\x1a.google.protobuf.TimestampR\x0ewarrantyExpiry\x12\x1a\n" +
	"\blocation\x18\n" +
	" \x01(\tR\blocation\x12\x1a\n" +
	"\blatitude\x18\v \x01(\x01R\blatitude\x12\x1c\n" +
	"\tlongitude\x18\f \x01(\x01R\tlongitude\x129\n" +
	"\x06status\x18\r \x01(\x0e2!.api.equipment.v1.EquipmentStatusR\x06status\x129\n" +
	"\x06health\x18\x0e \x01(\v2!.api.equipment.v1.EquipmentHealthR\x06health\x12?\n" +
	"\x0especifications\x18\x0f \x01(\v2\x17.google.protobuf.StructR\x0especifications\x123\n" +
	"\bmetadata\x18\x10 \x01(\v2\x17.google.protobuf.StructR\bmetadata\x129\n" +
	"\n" +
	"created_at\x18\x11 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\x12 \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"\x85\x02\n" +
	"\x0fEquipmentHealth\x12!\n" +
	"\fhealth_score\x18\x01 \x01(\x01R\vhealthScore\x12#\n" +
	"\rhealth_status\x18\x02 \x01(\tR\fhealthStatus\x128\n" +
	"\ametrics\x18\x03 \x03(\v2\x1e.api.equipment.v1.HealthMetricR\ametrics\x12C\n" +
	"\x0flast_assessment\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\x0elastAssessment\x12+\n" +
	"\x11assessment_method\x18\x05 \x01(\tR\x10assessmentMethod\"\xae\x01\n" +
	"\fHealthMetric\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x14\n" +
	"\x05value\x18\x02 \x01(\x01R\x05value\x12\x12\n" +
	"\x04unit\x18\x03 \x01(\tR\x04unit\x12#\n" +
	"\rthreshold_min\x18\x04 \x01(\x01R\fthresholdMin\x12#\n" +
	"\rthreshold_max\x18\x05 \x01(\x01R\fthresholdMax\x12\x16\n" +
	"\x06status\x18\x06 \x01(\tR\x06status\"\x8c\x04\n" +
	"\x16CreateEquipmentRequest\x12\x1f\n" +
	"\vcustomer_id\x18\x01 \x01(\x03R\n" +
	"customerId\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x12\n" +
	"\x04type\x18\x03 \x01(\tR\x04type\x12\x14\n" +
	"\x05brand\x18\x04 \x01(\tR\x05brand\x12\x14\n" +
	"\x05model\x18\x05 \x01(\tR\x05model\x12#\n" +
	"\rserial_number\x18\x06 \x01(\tR\fserialNumber\x12G\n" +
	"\x11installation_date\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\x10installationDate\x12C\n" +
	"\x0fwarranty_expiry\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\x0ewarrantyExpiry\x12\x1a\n" +
	"\blocation\x18\t \x01(\tR\blocation\x12\x1a\n" +
	"\blatitude\x18\n" +
	" \x01(\x01R\blatitude\x12\x1c\n" +
	"\tlongitude\x18\v \x01(\x01R\tlongitude\x12?\n" +
	"\x0especifications\x18\f \x01(\v2\x17.google.protobuf.StructR\x0especifications\x123\n" +
	"\bmetadata\x18\r \x01(\v2\x17.google.protobuf.StructR\bmetadata\"T\n" +
	"\x17CreateEquipmentResponse\x129\n" +
	"\tequipment\x18\x01 \x01(\v2\x1b.api.equipment.v1.EquipmentR\tequipment\"%\n" +
	"\x13GetEquipmentRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"Q\n" +
	"\x14GetEquipmentResponse\x129\n" +
	"\tequipment\x18\x01 \x01(\v2\x1b.api.equipment.v1.EquipmentR\tequipment\"\xb9\x01\n" +
	"\x14ListEquipmentRequest\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x02 \x01(\x05R\bpageSize\x12\x1f\n" +
	"\vcustomer_id\x18\x03 \x01(\x03R\n" +
	"customerId\x12\x12\n" +
	"\x04type\x18\x04 \x01(\tR\x04type\x12\x16\n" +
	"\x06status\x18\x05 \x01(\tR\x06status\x12#\n" +
	"\rhealth_status\x18\x06 \x01(\tR\fhealthStatus\"\xa4\x01\n" +
	"\x15ListEquipmentResponse\x129\n" +
	"\tequipment\x18\x01 \x03(\v2\x1b.api.equipment.v1.EquipmentR\tequipment\x12\x1f\n" +
	"\vtotal_count\x18\x02 \x01(\x05R\n" +
	"totalCount\x12\x12\n" +
	"\x04page\x18\x03 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x04 \x01(\x05R\bpageSize\"\xc3\x02\n" +
	"\x16UpdateEquipmentRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1a\n" +
	"\blocation\x18\x03 \x01(\tR\blocation\x12\x1a\n" +
	"\blatitude\x18\x04 \x01(\x01R\blatitude\x12\x1c\n" +
	"\tlongitude\x18\x05 \x01(\x01R\tlongitude\x129\n" +
	"\x06status\x18\x06 \x01(\x0e2!.api.equipment.v1.EquipmentStatusR\x06status\x12?\n" +
	"\x0especifications\x18\a \x01(\v2\x17.google.protobuf.StructR\x0especifications\x123\n" +
	"\bmetadata\x18\b \x01(\v2\x17.google.protobuf.StructR\bmetadata\"T\n" +
	"\x17UpdateEquipmentResponse\x129\n" +
	"\tequipment\x18\x01 \x01(\v2\x1b.api.equipment.v1.EquipmentR\tequipment\"(\n" +
	"\x16DeleteEquipmentRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"3\n" +
	"\x17DeleteEquipmentResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\"+\n" +
	"\x19GetEquipmentHealthRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"\x8e\x01\n" +
	"\x1aGetEquipmentHealthResponse\x129\n" +
	"\x06health\x18\x01 \x01(\v2!.api.equipment.v1.EquipmentHealthR\x06health\x125\n" +
	"\x06trends\x18\x02 \x03(\v2\x1d.api.equipment.v1.HealthTrendR\x06trends\"\x95\x01\n" +
	"\x1cUpdateEquipmentHealthRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x128\n" +
	"\ametrics\x18\x02 \x03(\v2\x1e.api.equipment.v1.HealthMetricR\ametrics\x12+\n" +
	"\x11assessment_method\x18\x03 \x01(\tR\x10assessmentMethod\"Z\n" +
	"\x1dUpdateEquipmentHealthResponse\x129\n" +
	"\x06health\x18\x01 \x01(\v2!.api.equipment.v1.EquipmentHealthR\x06health\"r\n" +
	"\vHealthTrend\x12\x1f\n" +
	"\vmetric_name\x18\x01 \x01(\tR\n" +
	"metricName\x12B\n" +
	"\vdata_points\x18\x02 \x03(\v2!.api.equipment.v1.HealthDataPointR\n" +
	"dataPoints\"a\n" +
	"\x0fHealthDataPoint\x128\n" +
	"\ttimestamp\x18\x01 \x01(\v2\x1a.google.protobuf.TimestampR\ttimestamp\x12\x14\n" +
	"\x05value\x18\x02 \x01(\x01R\x05value\"\xc6\x02\n" +
	"\x1aScheduleMaintenanceRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12)\n" +
	"\x10maintenance_type\x18\x02 \x01(\tR\x0fmaintenanceType\x12A\n" +
	"\x0escheduled_date\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\rscheduledDate\x12 \n" +
	"\vdescription\x18\x04 \x01(\tR\vdescription\x12#\n" +
	"\rtechnician_id\x18\x05 \x01(\x03R\ftechnicianId\x12%\n" +
	"\x0erequired_parts\x18\x06 \x03(\tR\rrequiredParts\x12<\n" +
	"\x1aestimated_duration_minutes\x18\a \x01(\x05R\x18estimatedDurationMinutes\"`\n" +
	"\x1bScheduleMaintenanceResponse\x12A\n" +
	"\bschedule\x18\x01 \x01(\v2%.api.equipment.v1.MaintenanceScheduleR\bschedule\"\x8a\x01\n" +
	"\x1cGetMaintenanceHistoryRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04page\x18\x02 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x03 \x01(\x05R\bpageSize\x12)\n" +
	"\x10maintenance_type\x18\x04 \x01(\tR\x0fmaintenanceType\"\x7f\n" +
	"\x1dGetMaintenanceHistoryResponse\x12=\n" +
	"\arecords\x18\x01 \x03(\v2#.api.equipment.v1.MaintenanceRecordR\arecords\x12\x1f\n" +
	"\vtotal_count\x18\x02 \x01(\x05R\n" +
	"totalCount\"\xb5\x03\n" +
	"\x13MaintenanceSchedule\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12!\n" +
	"\fequipment_id\x18\x02 \x01(\x03R\vequipmentId\x12)\n" +
	"\x10maintenance_type\x18\x03 \x01(\tR\x0fmaintenanceType\x12A\n" +
	"\x0escheduled_date\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\rscheduledDate\x12 \n" +
	"\vdescription\x18\x05 \x01(\tR\vdescription\x12#\n" +
	"\rtechnician_id\x18\x06 \x01(\x03R\ftechnicianId\x12%\n" +
	"\x0erequired_parts\x18\a \x03(\tR\rrequiredParts\x12<\n" +
	"\x1aestimated_duration_minutes\x18\b \x01(\x05R\x18estimatedDurationMinutes\x12\x16\n" +
	"\x06status\x18\t \x01(\tR\x06status\x129\n" +
	"\n" +
	"created_at\x18\n" +
	" \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\"\xcf\x03\n" +
	"\x11MaintenanceRecord\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12!\n" +
	"\fequipment_id\x18\x02 \x01(\x03R\vequipmentId\x12)\n" +
	"\x10maintenance_type\x18\x03 \x01(\tR\x0fmaintenanceType\x12A\n" +
	"\x0eperformed_date\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\rperformedDate\x12 \n" +
	"\vdescription\x18\x05 \x01(\tR\vdescription\x12#\n" +
	"\rtechnician_id\x18\x06 \x01(\x03R\ftechnicianId\x12\x1d\n" +
	"\n" +
	"parts_used\x18\a \x03(\tR\tpartsUsed\x126\n" +
	"\x17actual_duration_minutes\x18\b \x01(\x05R\x15actualDurationMinutes\x12\x16\n" +
	"\x06status\x18\t \x01(\tR\x06status\x12\x14\n" +
	"\x05notes\x18\n" +
	" \x01(\tR\x05notes\x12\x12\n" +
	"\x04cost\x18\v \x01(\x01R\x04cost\x129\n" +
	"\n" +
	"created_at\x18\f \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\"*\n" +
	"\x18GetEquipmentPartsRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"R\n" +
	"\x19GetEquipmentPartsResponse\x125\n" +
	"\x05parts\x18\x01 \x03(\v2\x1f.api.equipment.v1.EquipmentPartR\x05parts\"\xb7\x01\n" +
	"\x17UpdatePartStatusRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x17\n" +
	"\apart_id\x18\x02 \x01(\x03R\x06partId\x12\x16\n" +
	"\x06status\x18\x03 \x01(\tR\x06status\x12E\n" +
	"\x10replacement_date\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\x0freplacementDate\x12\x14\n" +
	"\x05notes\x18\x05 \x01(\tR\x05notes\"O\n" +
	"\x18UpdatePartStatusResponse\x123\n" +
	"\x04part\x18\x01 \x01(\v2\x1f.api.equipment.v1.EquipmentPartR\x04part\"\xb2\x03\n" +
	"\rEquipmentPart\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12!\n" +
	"\fequipment_id\x18\x02 \x01(\x03R\vequipmentId\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x1f\n" +
	"\vpart_number\x18\x04 \x01(\tR\n" +
	"partNumber\x12\"\n" +
	"\fmanufacturer\x18\x05 \x01(\tR\fmanufacturer\x12G\n" +
	"\x11installation_date\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\x10installationDate\x12C\n" +
	"\x0fwarranty_expiry\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\x0ewarrantyExpiry\x12\x16\n" +
	"\x06status\x18\b \x01(\tR\x06status\x12\x12\n" +
	"\x04cost\x18\t \x01(\x01R\x04cost\x12\x1a\n" +
	"\bsupplier\x18\n" +
	" \x01(\tR\bsupplier\x12?\n" +
	"\x0especifications\x18\v \x01(\v2\x17.google.protobuf.StructR\x0especifications\"\xa0\x01\n" +
	"\x1cGetEquipmentAnalyticsRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x129\n" +
	"\n" +
	"start_date\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\tstartDate\x125\n" +
	"\bend_date\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\aendDate\"c\n" +
	"\x1dGetEquipmentAnalyticsResponse\x12B\n" +
	"\tanalytics\x18\x01 \x01(\v2$.api.equipment.v1.EquipmentAnalyticsR\tanalytics\"a\n" +
	"\x17GetFleetOverviewRequest\x12\x1f\n" +
	"\vcustomer_id\x18\x01 \x01(\x03R\n" +
	"customerId\x12%\n" +
	"\x0eequipment_type\x18\x02 \x01(\tR\requipmentType\"W\n" +
	"\x18GetFleetOverviewResponse\x12;\n" +
	"\boverview\x18\x01 \x01(\v2\x1f.api.equipment.v1.FleetOverviewR\boverview\"\xf1\x03\n" +
	"\x12EquipmentAnalytics\x12!\n" +
	"\fequipment_id\x18\x01 \x01(\x03R\vequipmentId\x12+\n" +
	"\x11uptime_percentage\x18\x02 \x01(\x01R\x10uptimePercentage\x12+\n" +
	"\x11maintenance_count\x18\x03 \x01(\x05R\x10maintenanceCount\x124\n" +
	"\x16total_maintenance_cost\x18\x04 \x01(\x01R\x14totalMaintenanceCost\x126\n" +
	"\x17energy_efficiency_score\x18\x05 \x01(\x01R\x15energyEfficiencyScore\x12T\n" +
	"\x13performance_metrics\x18\x06 \x03(\v2#.api.equipment.v1.PerformanceMetricR\x12performanceMetrics\x12N\n" +
	"\x15analysis_period_start\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\x13analysisPeriodStart\x12J\n" +
	"\x13analysis_period_end\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\x11analysisPeriodEnd\"\xe2\x02\n" +
	"\rFleetOverview\x12'\n" +
	"\x0ftotal_equipment\x18\x01 \x01(\x05R\x0etotalEquipment\x12)\n" +
	"\x10active_equipment\x18\x02 \x01(\x05R\x0factiveEquipment\x12'\n" +
	"\x0fmaintenance_due\x18\x03 \x01(\x05R\x0emaintenanceDue\x12'\n" +
	"\x0fcritical_health\x18\x04 \x01(\x05R\x0ecriticalHealth\x120\n" +
	"\x14average_health_score\x18\x05 \x01(\x01R\x12averageHealthScore\x12*\n" +
	"\x11total_fleet_value\x18\x06 \x01(\x01R\x0ftotalFleetValue\x12M\n" +
	"\x0etype_summaries\x18\a \x03(\v2&.api.equipment.v1.EquipmentTypeSummaryR\rtypeSummaries\"\xae\x01\n" +
	"\x14EquipmentTypeSummary\x12%\n" +
	"\x0eequipment_type\x18\x01 \x01(\tR\requipmentType\x12\x14\n" +
	"\x05count\x18\x02 \x01(\x05R\x05count\x120\n" +
	"\x14average_health_score\x18\x03 \x01(\x01R\x12averageHealthScore\x12'\n" +
	"\x0fmaintenance_due\x18\x04 \x01(\x05R\x0emaintenanceDue\"\x85\x01\n" +
	"\x11PerformanceMetric\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x14\n" +
	"\x05value\x18\x02 \x01(\x01R\x05value\x12\x12\n" +
	"\x04unit\x18\x03 \x01(\tR\x04unit\x12\x1c\n" +
	"\tbenchmark\x18\x04 \x01(\x01R\tbenchmark\x12\x14\n" +
	"\x05trend\x18\x05 \x01(\tR\x05trend\"S\n" +
	"\x15GenerateQRCodeRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x16\n" +
	"\x06format\x18\x02 \x01(\tR\x06format\x12\x12\n" +
	"\x04size\x18\x03 \x01(\x05R\x04size\"r\n" +
	"\x16GenerateQRCodeResponse\x12\x1e\n" +
	"\vqr_code_url\x18\x01 \x01(\tR\tqrCodeUrl\x12 \n" +
	"\fqr_code_data\x18\x02 \x01(\fR\n" +
	"qrCodeData\x12\x16\n" +
	"\x06format\x18\x03 \x01(\tR\x06format*\xcc\x01\n" +
	"\x0fEquipmentStatus\x12 \n" +
	"\x1cEQUIPMENT_STATUS_UNSPECIFIED\x10\x00\x12\x1b\n" +
	"\x17EQUIPMENT_STATUS_ACTIVE\x10\x01\x12\x1d\n" +
	"\x19EQUIPMENT_STATUS_INACTIVE\x10\x02\x12 \n" +
	"\x1cEQUIPMENT_STATUS_MAINTENANCE\x10\x03\x12\x1c\n" +
	"\x18EQUIPMENT_STATUS_RETIRED\x10\x04\x12\x1b\n" +
	"\x17EQUIPMENT_STATUS_FAULTY\x10\x052\xc4\x10\n" +
	"\x10EquipmentService\x12\x84\x01\n" +
	"\x0fCreateEquipment\x12(.api.equipment.v1.CreateEquipmentRequest\x1a).api.equipment.v1.CreateEquipmentResponse\"\x1c\x82\xd3\xe4\x93\x02\x16:\x01*\"\x11/api/v1/equipment\x12}\n" +
	"\fGetEquipment\x12%.api.equipment.v1.GetEquipmentRequest\x1a&.api.equipment.v1.GetEquipmentResponse\"\x1e\x82\xd3\xe4\x93\x02\x18\x12\x16/api/v1/equipment/{id}\x12{\n" +
	"\rListEquipment\x12&.api.equipment.v1.ListEquipmentRequest\x1a'.api.equipment.v1.ListEquipmentResponse\"\x19\x82\xd3\xe4\x93\x02\x13\x12\x11/api/v1/equipment\x12\x89\x01\n" +
	"\x0fUpdateEquipment\x12(.api.equipment.v1.UpdateEquipmentRequest\x1a).api.equipment.v1.UpdateEquipmentResponse\"!\x82\xd3\xe4\x93\x02\x1b:\x01*\x1a\x16/api/v1/equipment/{id}\x12\x86\x01\n" +
	"\x0fDeleteEquipment\x12(.api.equipment.v1.DeleteEquipmentRequest\x1a).api.equipment.v1.DeleteEquipmentResponse\"\x1e\x82\xd3\xe4\x93\x02\x18*\x16/api/v1/equipment/{id}\x12\x96\x01\n" +
	"\x12GetEquipmentHealth\x12+.api.equipment.v1.GetEquipmentHealthRequest\x1a,.api.equipment.v1.GetEquipmentHealthResponse\"%\x82\xd3\xe4\x93\x02\x1f\x12\x1d/api/v1/equipment/{id}/health\x12\xa2\x01\n" +
	"\x15UpdateEquipmentHealth\x12..api.equipment.v1.UpdateEquipmentHealthRequest\x1a/.api.equipment.v1.UpdateEquipmentHealthResponse\"(\x82\xd3\xe4\x93\x02\":\x01*\"\x1d/api/v1/equipment/{id}/health\x12\xaa\x01\n" +
	"\x13ScheduleMaintenance\x12,.api.equipment.v1.ScheduleMaintenanceRequest\x1a-.api.equipment.v1.ScheduleMaintenanceResponse\"6\x82\xd3\xe4\x93\x020:\x01*\"+/api/v1/equipment/{id}/maintenance/schedule\x12\xac\x01\n" +
	"\x15GetMaintenanceHistory\x12..api.equipment.v1.GetMaintenanceHistoryRequest\x1a/.api.equipment.v1.GetMaintenanceHistoryResponse\"2\x82\xd3\xe4\x93\x02,\x12*/api/v1/equipment/{id}/maintenance/history\x12\x92\x01\n" +
	"\x11GetEquipmentParts\x12*.api.equipment.v1.GetEquipmentPartsRequest\x1a+.api.equipment.v1.GetEquipmentPartsResponse\"$\x82\xd3\xe4\x93\x02\x1e\x12\x1c/api/v1/equipment/{id}/parts\x12\x9c\x01\n" +
	"\x10UpdatePartStatus\x12).api.equipment.v1.UpdatePartStatusRequest\x1a*.api.equipment.v1.UpdatePartStatusResponse\"1\x82\xd3\xe4\x93\x02+:\x01*\x1a&/api/v1/equipment/{id}/parts/{part_id}\x12\xa2\x01\n" +
	"\x15GetEquipmentAnalytics\x12..api.equipment.v1.GetEquipmentAnalyticsRequest\x1a/.api.equipment.v1.GetEquipmentAnalyticsResponse\"(\x82\xd3\xe4\x93\x02\"\x12 /api/v1/equipment/{id}/analytics\x12\x93\x01\n" +
	"\x10GetFleetOverview\x12).api.equipment.v1.GetFleetOverviewRequest\x1a*.api.equipment.v1.GetFleetOverviewResponse\"(\x82\xd3\xe4\x93\x02\"\x12 /api/v1/equipment/fleet/overview\x12\x8e\x01\n" +
	"\x0eGenerateQRCode\x12'.api.equipment.v1.GenerateQRCodeRequest\x1a(.api.equipment.v1.GenerateQRCodeResponse\")\x82\xd3\xe4\x93\x02#:\x01*\"\x1e/api/v1/equipment/{id}/qr-codeB+Z)gobackend-hvac-kratos/api/equipment/v1;v1b\x06proto3"

var (
	file_equipment_v1_equipment_proto_rawDescOnce sync.Once
	file_equipment_v1_equipment_proto_rawDescData []byte
)

func file_equipment_v1_equipment_proto_rawDescGZIP() []byte {
	file_equipment_v1_equipment_proto_rawDescOnce.Do(func() {
		file_equipment_v1_equipment_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_equipment_v1_equipment_proto_rawDesc), len(file_equipment_v1_equipment_proto_rawDesc)))
	})
	return file_equipment_v1_equipment_proto_rawDescData
}

var file_equipment_v1_equipment_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_equipment_v1_equipment_proto_msgTypes = make([]protoimpl.MessageInfo, 40)
var file_equipment_v1_equipment_proto_goTypes = []any{
	(EquipmentStatus)(0),                  // 0: api.equipment.v1.EquipmentStatus
	(*Equipment)(nil),                     // 1: api.equipment.v1.Equipment
	(*EquipmentHealth)(nil),               // 2: api.equipment.v1.EquipmentHealth
	(*HealthMetric)(nil),                  // 3: api.equipment.v1.HealthMetric
	(*CreateEquipmentRequest)(nil),        // 4: api.equipment.v1.CreateEquipmentRequest
	(*CreateEquipmentResponse)(nil),       // 5: api.equipment.v1.CreateEquipmentResponse
	(*GetEquipmentRequest)(nil),           // 6: api.equipment.v1.GetEquipmentRequest
	(*GetEquipmentResponse)(nil),          // 7: api.equipment.v1.GetEquipmentResponse
	(*ListEquipmentRequest)(nil),          // 8: api.equipment.v1.ListEquipmentRequest
	(*ListEquipmentResponse)(nil),         // 9: api.equipment.v1.ListEquipmentResponse
	(*UpdateEquipmentRequest)(nil),        // 10: api.equipment.v1.UpdateEquipmentRequest
	(*UpdateEquipmentResponse)(nil),       // 11: api.equipment.v1.UpdateEquipmentResponse
	(*DeleteEquipmentRequest)(nil),        // 12: api.equipment.v1.DeleteEquipmentRequest
	(*DeleteEquipmentResponse)(nil),       // 13: api.equipment.v1.DeleteEquipmentResponse
	(*GetEquipmentHealthRequest)(nil),     // 14: api.equipment.v1.GetEquipmentHealthRequest
	(*GetEquipmentHealthResponse)(nil),    // 15: api.equipment.v1.GetEquipmentHealthResponse
	(*UpdateEquipmentHealthRequest)(nil),  // 16: api.equipment.v1.UpdateEquipmentHealthRequest
	(*UpdateEquipmentHealthResponse)(nil), // 17: api.equipment.v1.UpdateEquipmentHealthResponse
	(*HealthTrend)(nil),                   // 18: api.equipment.v1.HealthTrend
	(*HealthDataPoint)(nil),               // 19: api.equipment.v1.HealthDataPoint
	(*ScheduleMaintenanceRequest)(nil),    // 20: api.equipment.v1.ScheduleMaintenanceRequest
	(*ScheduleMaintenanceResponse)(nil),   // 21: api.equipment.v1.ScheduleMaintenanceResponse
	(*GetMaintenanceHistoryRequest)(nil),  // 22: api.equipment.v1.GetMaintenanceHistoryRequest
	(*GetMaintenanceHistoryResponse)(nil), // 23: api.equipment.v1.GetMaintenanceHistoryResponse
	(*MaintenanceSchedule)(nil),           // 24: api.equipment.v1.MaintenanceSchedule
	(*MaintenanceRecord)(nil),             // 25: api.equipment.v1.MaintenanceRecord
	(*GetEquipmentPartsRequest)(nil),      // 26: api.equipment.v1.GetEquipmentPartsRequest
	(*GetEquipmentPartsResponse)(nil),     // 27: api.equipment.v1.GetEquipmentPartsResponse
	(*UpdatePartStatusRequest)(nil),       // 28: api.equipment.v1.UpdatePartStatusRequest
	(*UpdatePartStatusResponse)(nil),      // 29: api.equipment.v1.UpdatePartStatusResponse
	(*EquipmentPart)(nil),                 // 30: api.equipment.v1.EquipmentPart
	(*GetEquipmentAnalyticsRequest)(nil),  // 31: api.equipment.v1.GetEquipmentAnalyticsRequest
	(*GetEquipmentAnalyticsResponse)(nil), // 32: api.equipment.v1.GetEquipmentAnalyticsResponse
	(*GetFleetOverviewRequest)(nil),       // 33: api.equipment.v1.GetFleetOverviewRequest
	(*GetFleetOverviewResponse)(nil),      // 34: api.equipment.v1.GetFleetOverviewResponse
	(*EquipmentAnalytics)(nil),            // 35: api.equipment.v1.EquipmentAnalytics
	(*FleetOverview)(nil),                 // 36: api.equipment.v1.FleetOverview
	(*EquipmentTypeSummary)(nil),          // 37: api.equipment.v1.EquipmentTypeSummary
	(*PerformanceMetric)(nil),             // 38: api.equipment.v1.PerformanceMetric
	(*GenerateQRCodeRequest)(nil),         // 39: api.equipment.v1.GenerateQRCodeRequest
	(*GenerateQRCodeResponse)(nil),        // 40: api.equipment.v1.GenerateQRCodeResponse
	(*timestamppb.Timestamp)(nil),         // 41: google.protobuf.Timestamp
	(*structpb.Struct)(nil),               // 42: google.protobuf.Struct
}
var file_equipment_v1_equipment_proto_depIdxs = []int32{
	41, // 0: api.equipment.v1.Equipment.installation_date:type_name -> google.protobuf.Timestamp
	41, // 1: api.equipment.v1.Equipment.warranty_expiry:type_name -> google.protobuf.Timestamp
	0,  // 2: api.equipment.v1.Equipment.status:type_name -> api.equipment.v1.EquipmentStatus
	2,  // 3: api.equipment.v1.Equipment.health:type_name -> api.equipment.v1.EquipmentHealth
	42, // 4: api.equipment.v1.Equipment.specifications:type_name -> google.protobuf.Struct
	42, // 5: api.equipment.v1.Equipment.metadata:type_name -> google.protobuf.Struct
	41, // 6: api.equipment.v1.Equipment.created_at:type_name -> google.protobuf.Timestamp
	41, // 7: api.equipment.v1.Equipment.updated_at:type_name -> google.protobuf.Timestamp
	3,  // 8: api.equipment.v1.EquipmentHealth.metrics:type_name -> api.equipment.v1.HealthMetric
	41, // 9: api.equipment.v1.EquipmentHealth.last_assessment:type_name -> google.protobuf.Timestamp
	41, // 10: api.equipment.v1.CreateEquipmentRequest.installation_date:type_name -> google.protobuf.Timestamp
	41, // 11: api.equipment.v1.CreateEquipmentRequest.warranty_expiry:type_name -> google.protobuf.Timestamp
	42, // 12: api.equipment.v1.CreateEquipmentRequest.specifications:type_name -> google.protobuf.Struct
	42, // 13: api.equipment.v1.CreateEquipmentRequest.metadata:type_name -> google.protobuf.Struct
	1,  // 14: api.equipment.v1.CreateEquipmentResponse.equipment:type_name -> api.equipment.v1.Equipment
	1,  // 15: api.equipment.v1.GetEquipmentResponse.equipment:type_name -> api.equipment.v1.Equipment
	1,  // 16: api.equipment.v1.ListEquipmentResponse.equipment:type_name -> api.equipment.v1.Equipment
	0,  // 17: api.equipment.v1.UpdateEquipmentRequest.status:type_name -> api.equipment.v1.EquipmentStatus
	42, // 18: api.equipment.v1.UpdateEquipmentRequest.specifications:type_name -> google.protobuf.Struct
	42, // 19: api.equipment.v1.UpdateEquipmentRequest.metadata:type_name -> google.protobuf.Struct
	1,  // 20: api.equipment.v1.UpdateEquipmentResponse.equipment:type_name -> api.equipment.v1.Equipment
	2,  // 21: api.equipment.v1.GetEquipmentHealthResponse.health:type_name -> api.equipment.v1.EquipmentHealth
	18, // 22: api.equipment.v1.GetEquipmentHealthResponse.trends:type_name -> api.equipment.v1.HealthTrend
	3,  // 23: api.equipment.v1.UpdateEquipmentHealthRequest.metrics:type_name -> api.equipment.v1.HealthMetric
	2,  // 24: api.equipment.v1.UpdateEquipmentHealthResponse.health:type_name -> api.equipment.v1.EquipmentHealth
	19, // 25: api.equipment.v1.HealthTrend.data_points:type_name -> api.equipment.v1.HealthDataPoint
	41, // 26: api.equipment.v1.HealthDataPoint.timestamp:type_name -> google.protobuf.Timestamp
	41, // 27: api.equipment.v1.ScheduleMaintenanceRequest.scheduled_date:type_name -> google.protobuf.Timestamp
	24, // 28: api.equipment.v1.ScheduleMaintenanceResponse.schedule:type_name -> api.equipment.v1.MaintenanceSchedule
	25, // 29: api.equipment.v1.GetMaintenanceHistoryResponse.records:type_name -> api.equipment.v1.MaintenanceRecord
	41, // 30: api.equipment.v1.MaintenanceSchedule.scheduled_date:type_name -> google.protobuf.Timestamp
	41, // 31: api.equipment.v1.MaintenanceSchedule.created_at:type_name -> google.protobuf.Timestamp
	41, // 32: api.equipment.v1.MaintenanceRecord.performed_date:type_name -> google.protobuf.Timestamp
	41, // 33: api.equipment.v1.MaintenanceRecord.created_at:type_name -> google.protobuf.Timestamp
	30, // 34: api.equipment.v1.GetEquipmentPartsResponse.parts:type_name -> api.equipment.v1.EquipmentPart
	41, // 35: api.equipment.v1.UpdatePartStatusRequest.replacement_date:type_name -> google.protobuf.Timestamp
	30, // 36: api.equipment.v1.UpdatePartStatusResponse.part:type_name -> api.equipment.v1.EquipmentPart
	41, // 37: api.equipment.v1.EquipmentPart.installation_date:type_name -> google.protobuf.Timestamp
	41, // 38: api.equipment.v1.EquipmentPart.warranty_expiry:type_name -> google.protobuf.Timestamp
	42, // 39: api.equipment.v1.EquipmentPart.specifications:type_name -> google.protobuf.Struct
	41, // 40: api.equipment.v1.GetEquipmentAnalyticsRequest.start_date:type_name -> google.protobuf.Timestamp
	41, // 41: api.equipment.v1.GetEquipmentAnalyticsRequest.end_date:type_name -> google.protobuf.Timestamp
	35, // 42: api.equipment.v1.GetEquipmentAnalyticsResponse.analytics:type_name -> api.equipment.v1.EquipmentAnalytics
	36, // 43: api.equipment.v1.GetFleetOverviewResponse.overview:type_name -> api.equipment.v1.FleetOverview
	38, // 44: api.equipment.v1.EquipmentAnalytics.performance_metrics:type_name -> api.equipment.v1.PerformanceMetric
	41, // 45: api.equipment.v1.EquipmentAnalytics.analysis_period_start:type_name -> google.protobuf.Timestamp
	41, // 46: api.equipment.v1.EquipmentAnalytics.analysis_period_end:type_name -> google.protobuf.Timestamp
	37, // 47: api.equipment.v1.FleetOverview.type_summaries:type_name -> api.equipment.v1.EquipmentTypeSummary
	4,  // 48: api.equipment.v1.EquipmentService.CreateEquipment:input_type -> api.equipment.v1.CreateEquipmentRequest
	6,  // 49: api.equipment.v1.EquipmentService.GetEquipment:input_type -> api.equipment.v1.GetEquipmentRequest
	8,  // 50: api.equipment.v1.EquipmentService.ListEquipment:input_type -> api.equipment.v1.ListEquipmentRequest
	10, // 51: api.equipment.v1.EquipmentService.UpdateEquipment:input_type -> api.equipment.v1.UpdateEquipmentRequest
	12, // 52: api.equipment.v1.EquipmentService.DeleteEquipment:input_type -> api.equipment.v1.DeleteEquipmentRequest
	14, // 53: api.equipment.v1.EquipmentService.GetEquipmentHealth:input_type -> api.equipment.v1.GetEquipmentHealthRequest
	16, // 54: api.equipment.v1.EquipmentService.UpdateEquipmentHealth:input_type -> api.equipment.v1.UpdateEquipmentHealthRequest
	20, // 55: api.equipment.v1.EquipmentService.ScheduleMaintenance:input_type -> api.equipment.v1.ScheduleMaintenanceRequest
	22, // 56: api.equipment.v1.EquipmentService.GetMaintenanceHistory:input_type -> api.equipment.v1.GetMaintenanceHistoryRequest
	26, // 57: api.equipment.v1.EquipmentService.GetEquipmentParts:input_type -> api.equipment.v1.GetEquipmentPartsRequest
	28, // 58: api.equipment.v1.EquipmentService.UpdatePartStatus:input_type -> api.equipment.v1.UpdatePartStatusRequest
	31, // 59: api.equipment.v1.EquipmentService.GetEquipmentAnalytics:input_type -> api.equipment.v1.GetEquipmentAnalyticsRequest
	33, // 60: api.equipment.v1.EquipmentService.GetFleetOverview:input_type -> api.equipment.v1.GetFleetOverviewRequest
	39, // 61: api.equipment.v1.EquipmentService.GenerateQRCode:input_type -> api.equipment.v1.GenerateQRCodeRequest
	5,  // 62: api.equipment.v1.EquipmentService.CreateEquipment:output_type -> api.equipment.v1.CreateEquipmentResponse
	7,  // 63: api.equipment.v1.EquipmentService.GetEquipment:output_type -> api.equipment.v1.GetEquipmentResponse
	9,  // 64: api.equipment.v1.EquipmentService.ListEquipment:output_type -> api.equipment.v1.ListEquipmentResponse
	11, // 65: api.equipment.v1.EquipmentService.UpdateEquipment:output_type -> api.equipment.v1.UpdateEquipmentResponse
	13, // 66: api.equipment.v1.EquipmentService.DeleteEquipment:output_type -> api.equipment.v1.DeleteEquipmentResponse
	15, // 67: api.equipment.v1.EquipmentService.GetEquipmentHealth:output_type -> api.equipment.v1.GetEquipmentHealthResponse
	17, // 68: api.equipment.v1.EquipmentService.UpdateEquipmentHealth:output_type -> api.equipment.v1.UpdateEquipmentHealthResponse
	21, // 69: api.equipment.v1.EquipmentService.ScheduleMaintenance:output_type -> api.equipment.v1.ScheduleMaintenanceResponse
	23, // 70: api.equipment.v1.EquipmentService.GetMaintenanceHistory:output_type -> api.equipment.v1.GetMaintenanceHistoryResponse
	27, // 71: api.equipment.v1.EquipmentService.GetEquipmentParts:output_type -> api.equipment.v1.GetEquipmentPartsResponse
	29, // 72: api.equipment.v1.EquipmentService.UpdatePartStatus:output_type -> api.equipment.v1.UpdatePartStatusResponse
	32, // 73: api.equipment.v1.EquipmentService.GetEquipmentAnalytics:output_type -> api.equipment.v1.GetEquipmentAnalyticsResponse
	34, // 74: api.equipment.v1.EquipmentService.GetFleetOverview:output_type -> api.equipment.v1.GetFleetOverviewResponse
	40, // 75: api.equipment.v1.EquipmentService.GenerateQRCode:output_type -> api.equipment.v1.GenerateQRCodeResponse
	62, // [62:76] is the sub-list for method output_type
	48, // [48:62] is the sub-list for method input_type
	48, // [48:48] is the sub-list for extension type_name
	48, // [48:48] is the sub-list for extension extendee
	0,  // [0:48] is the sub-list for field type_name
}

func init() { file_equipment_v1_equipment_proto_init() }
func file_equipment_v1_equipment_proto_init() {
	if File_equipment_v1_equipment_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_equipment_v1_equipment_proto_rawDesc), len(file_equipment_v1_equipment_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   40,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_equipment_v1_equipment_proto_goTypes,
		DependencyIndexes: file_equipment_v1_equipment_proto_depIdxs,
		EnumInfos:         file_equipment_v1_equipment_proto_enumTypes,
		MessageInfos:      file_equipment_v1_equipment_proto_msgTypes,
	}.Build()
	File_equipment_v1_equipment_proto = out.File
	file_equipment_v1_equipment_proto_goTypes = nil
	file_equipment_v1_equipment_proto_depIdxs = nil
}
