// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v3.21.12
// source: equipment/v1/equipment.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	EquipmentService_CreateEquipment_FullMethodName       = "/api.equipment.v1.EquipmentService/CreateEquipment"
	EquipmentService_GetEquipment_FullMethodName          = "/api.equipment.v1.EquipmentService/GetEquipment"
	EquipmentService_ListEquipment_FullMethodName         = "/api.equipment.v1.EquipmentService/ListEquipment"
	EquipmentService_UpdateEquipment_FullMethodName       = "/api.equipment.v1.EquipmentService/UpdateEquipment"
	EquipmentService_DeleteEquipment_FullMethodName       = "/api.equipment.v1.EquipmentService/DeleteEquipment"
	EquipmentService_GetEquipmentHealth_FullMethodName    = "/api.equipment.v1.EquipmentService/GetEquipmentHealth"
	EquipmentService_UpdateEquipmentHealth_FullMethodName = "/api.equipment.v1.EquipmentService/UpdateEquipmentHealth"
	EquipmentService_ScheduleMaintenance_FullMethodName   = "/api.equipment.v1.EquipmentService/ScheduleMaintenance"
	EquipmentService_GetMaintenanceHistory_FullMethodName = "/api.equipment.v1.EquipmentService/GetMaintenanceHistory"
	EquipmentService_GetEquipmentParts_FullMethodName     = "/api.equipment.v1.EquipmentService/GetEquipmentParts"
	EquipmentService_UpdatePartStatus_FullMethodName      = "/api.equipment.v1.EquipmentService/UpdatePartStatus"
	EquipmentService_GetEquipmentAnalytics_FullMethodName = "/api.equipment.v1.EquipmentService/GetEquipmentAnalytics"
	EquipmentService_GetFleetOverview_FullMethodName      = "/api.equipment.v1.EquipmentService/GetFleetOverview"
	EquipmentService_GenerateQRCode_FullMethodName        = "/api.equipment.v1.EquipmentService/GenerateQRCode"
)

// EquipmentServiceClient is the client API for EquipmentService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 🏭 Equipment Registry Service - Comprehensive Equipment Management
type EquipmentServiceClient interface {
	// Equipment CRUD Operations
	CreateEquipment(ctx context.Context, in *CreateEquipmentRequest, opts ...grpc.CallOption) (*CreateEquipmentResponse, error)
	GetEquipment(ctx context.Context, in *GetEquipmentRequest, opts ...grpc.CallOption) (*GetEquipmentResponse, error)
	ListEquipment(ctx context.Context, in *ListEquipmentRequest, opts ...grpc.CallOption) (*ListEquipmentResponse, error)
	UpdateEquipment(ctx context.Context, in *UpdateEquipmentRequest, opts ...grpc.CallOption) (*UpdateEquipmentResponse, error)
	DeleteEquipment(ctx context.Context, in *DeleteEquipmentRequest, opts ...grpc.CallOption) (*DeleteEquipmentResponse, error)
	// Health Monitoring
	GetEquipmentHealth(ctx context.Context, in *GetEquipmentHealthRequest, opts ...grpc.CallOption) (*GetEquipmentHealthResponse, error)
	UpdateEquipmentHealth(ctx context.Context, in *UpdateEquipmentHealthRequest, opts ...grpc.CallOption) (*UpdateEquipmentHealthResponse, error)
	// Maintenance Management
	ScheduleMaintenance(ctx context.Context, in *ScheduleMaintenanceRequest, opts ...grpc.CallOption) (*ScheduleMaintenanceResponse, error)
	GetMaintenanceHistory(ctx context.Context, in *GetMaintenanceHistoryRequest, opts ...grpc.CallOption) (*GetMaintenanceHistoryResponse, error)
	// Parts Management
	GetEquipmentParts(ctx context.Context, in *GetEquipmentPartsRequest, opts ...grpc.CallOption) (*GetEquipmentPartsResponse, error)
	UpdatePartStatus(ctx context.Context, in *UpdatePartStatusRequest, opts ...grpc.CallOption) (*UpdatePartStatusResponse, error)
	// Analytics and Reporting
	GetEquipmentAnalytics(ctx context.Context, in *GetEquipmentAnalyticsRequest, opts ...grpc.CallOption) (*GetEquipmentAnalyticsResponse, error)
	GetFleetOverview(ctx context.Context, in *GetFleetOverviewRequest, opts ...grpc.CallOption) (*GetFleetOverviewResponse, error)
	// QR Code and Identification
	GenerateQRCode(ctx context.Context, in *GenerateQRCodeRequest, opts ...grpc.CallOption) (*GenerateQRCodeResponse, error)
}

type equipmentServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewEquipmentServiceClient(cc grpc.ClientConnInterface) EquipmentServiceClient {
	return &equipmentServiceClient{cc}
}

func (c *equipmentServiceClient) CreateEquipment(ctx context.Context, in *CreateEquipmentRequest, opts ...grpc.CallOption) (*CreateEquipmentResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateEquipmentResponse)
	err := c.cc.Invoke(ctx, EquipmentService_CreateEquipment_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *equipmentServiceClient) GetEquipment(ctx context.Context, in *GetEquipmentRequest, opts ...grpc.CallOption) (*GetEquipmentResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetEquipmentResponse)
	err := c.cc.Invoke(ctx, EquipmentService_GetEquipment_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *equipmentServiceClient) ListEquipment(ctx context.Context, in *ListEquipmentRequest, opts ...grpc.CallOption) (*ListEquipmentResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListEquipmentResponse)
	err := c.cc.Invoke(ctx, EquipmentService_ListEquipment_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *equipmentServiceClient) UpdateEquipment(ctx context.Context, in *UpdateEquipmentRequest, opts ...grpc.CallOption) (*UpdateEquipmentResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateEquipmentResponse)
	err := c.cc.Invoke(ctx, EquipmentService_UpdateEquipment_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *equipmentServiceClient) DeleteEquipment(ctx context.Context, in *DeleteEquipmentRequest, opts ...grpc.CallOption) (*DeleteEquipmentResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteEquipmentResponse)
	err := c.cc.Invoke(ctx, EquipmentService_DeleteEquipment_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *equipmentServiceClient) GetEquipmentHealth(ctx context.Context, in *GetEquipmentHealthRequest, opts ...grpc.CallOption) (*GetEquipmentHealthResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetEquipmentHealthResponse)
	err := c.cc.Invoke(ctx, EquipmentService_GetEquipmentHealth_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *equipmentServiceClient) UpdateEquipmentHealth(ctx context.Context, in *UpdateEquipmentHealthRequest, opts ...grpc.CallOption) (*UpdateEquipmentHealthResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateEquipmentHealthResponse)
	err := c.cc.Invoke(ctx, EquipmentService_UpdateEquipmentHealth_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *equipmentServiceClient) ScheduleMaintenance(ctx context.Context, in *ScheduleMaintenanceRequest, opts ...grpc.CallOption) (*ScheduleMaintenanceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ScheduleMaintenanceResponse)
	err := c.cc.Invoke(ctx, EquipmentService_ScheduleMaintenance_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *equipmentServiceClient) GetMaintenanceHistory(ctx context.Context, in *GetMaintenanceHistoryRequest, opts ...grpc.CallOption) (*GetMaintenanceHistoryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetMaintenanceHistoryResponse)
	err := c.cc.Invoke(ctx, EquipmentService_GetMaintenanceHistory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *equipmentServiceClient) GetEquipmentParts(ctx context.Context, in *GetEquipmentPartsRequest, opts ...grpc.CallOption) (*GetEquipmentPartsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetEquipmentPartsResponse)
	err := c.cc.Invoke(ctx, EquipmentService_GetEquipmentParts_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *equipmentServiceClient) UpdatePartStatus(ctx context.Context, in *UpdatePartStatusRequest, opts ...grpc.CallOption) (*UpdatePartStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdatePartStatusResponse)
	err := c.cc.Invoke(ctx, EquipmentService_UpdatePartStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *equipmentServiceClient) GetEquipmentAnalytics(ctx context.Context, in *GetEquipmentAnalyticsRequest, opts ...grpc.CallOption) (*GetEquipmentAnalyticsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetEquipmentAnalyticsResponse)
	err := c.cc.Invoke(ctx, EquipmentService_GetEquipmentAnalytics_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *equipmentServiceClient) GetFleetOverview(ctx context.Context, in *GetFleetOverviewRequest, opts ...grpc.CallOption) (*GetFleetOverviewResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetFleetOverviewResponse)
	err := c.cc.Invoke(ctx, EquipmentService_GetFleetOverview_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *equipmentServiceClient) GenerateQRCode(ctx context.Context, in *GenerateQRCodeRequest, opts ...grpc.CallOption) (*GenerateQRCodeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GenerateQRCodeResponse)
	err := c.cc.Invoke(ctx, EquipmentService_GenerateQRCode_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EquipmentServiceServer is the server API for EquipmentService service.
// All implementations must embed UnimplementedEquipmentServiceServer
// for forward compatibility.
//
// 🏭 Equipment Registry Service - Comprehensive Equipment Management
type EquipmentServiceServer interface {
	// Equipment CRUD Operations
	CreateEquipment(context.Context, *CreateEquipmentRequest) (*CreateEquipmentResponse, error)
	GetEquipment(context.Context, *GetEquipmentRequest) (*GetEquipmentResponse, error)
	ListEquipment(context.Context, *ListEquipmentRequest) (*ListEquipmentResponse, error)
	UpdateEquipment(context.Context, *UpdateEquipmentRequest) (*UpdateEquipmentResponse, error)
	DeleteEquipment(context.Context, *DeleteEquipmentRequest) (*DeleteEquipmentResponse, error)
	// Health Monitoring
	GetEquipmentHealth(context.Context, *GetEquipmentHealthRequest) (*GetEquipmentHealthResponse, error)
	UpdateEquipmentHealth(context.Context, *UpdateEquipmentHealthRequest) (*UpdateEquipmentHealthResponse, error)
	// Maintenance Management
	ScheduleMaintenance(context.Context, *ScheduleMaintenanceRequest) (*ScheduleMaintenanceResponse, error)
	GetMaintenanceHistory(context.Context, *GetMaintenanceHistoryRequest) (*GetMaintenanceHistoryResponse, error)
	// Parts Management
	GetEquipmentParts(context.Context, *GetEquipmentPartsRequest) (*GetEquipmentPartsResponse, error)
	UpdatePartStatus(context.Context, *UpdatePartStatusRequest) (*UpdatePartStatusResponse, error)
	// Analytics and Reporting
	GetEquipmentAnalytics(context.Context, *GetEquipmentAnalyticsRequest) (*GetEquipmentAnalyticsResponse, error)
	GetFleetOverview(context.Context, *GetFleetOverviewRequest) (*GetFleetOverviewResponse, error)
	// QR Code and Identification
	GenerateQRCode(context.Context, *GenerateQRCodeRequest) (*GenerateQRCodeResponse, error)
	mustEmbedUnimplementedEquipmentServiceServer()
}

// UnimplementedEquipmentServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedEquipmentServiceServer struct{}

func (UnimplementedEquipmentServiceServer) CreateEquipment(context.Context, *CreateEquipmentRequest) (*CreateEquipmentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateEquipment not implemented")
}
func (UnimplementedEquipmentServiceServer) GetEquipment(context.Context, *GetEquipmentRequest) (*GetEquipmentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEquipment not implemented")
}
func (UnimplementedEquipmentServiceServer) ListEquipment(context.Context, *ListEquipmentRequest) (*ListEquipmentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListEquipment not implemented")
}
func (UnimplementedEquipmentServiceServer) UpdateEquipment(context.Context, *UpdateEquipmentRequest) (*UpdateEquipmentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateEquipment not implemented")
}
func (UnimplementedEquipmentServiceServer) DeleteEquipment(context.Context, *DeleteEquipmentRequest) (*DeleteEquipmentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteEquipment not implemented")
}
func (UnimplementedEquipmentServiceServer) GetEquipmentHealth(context.Context, *GetEquipmentHealthRequest) (*GetEquipmentHealthResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEquipmentHealth not implemented")
}
func (UnimplementedEquipmentServiceServer) UpdateEquipmentHealth(context.Context, *UpdateEquipmentHealthRequest) (*UpdateEquipmentHealthResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateEquipmentHealth not implemented")
}
func (UnimplementedEquipmentServiceServer) ScheduleMaintenance(context.Context, *ScheduleMaintenanceRequest) (*ScheduleMaintenanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ScheduleMaintenance not implemented")
}
func (UnimplementedEquipmentServiceServer) GetMaintenanceHistory(context.Context, *GetMaintenanceHistoryRequest) (*GetMaintenanceHistoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMaintenanceHistory not implemented")
}
func (UnimplementedEquipmentServiceServer) GetEquipmentParts(context.Context, *GetEquipmentPartsRequest) (*GetEquipmentPartsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEquipmentParts not implemented")
}
func (UnimplementedEquipmentServiceServer) UpdatePartStatus(context.Context, *UpdatePartStatusRequest) (*UpdatePartStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePartStatus not implemented")
}
func (UnimplementedEquipmentServiceServer) GetEquipmentAnalytics(context.Context, *GetEquipmentAnalyticsRequest) (*GetEquipmentAnalyticsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEquipmentAnalytics not implemented")
}
func (UnimplementedEquipmentServiceServer) GetFleetOverview(context.Context, *GetFleetOverviewRequest) (*GetFleetOverviewResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFleetOverview not implemented")
}
func (UnimplementedEquipmentServiceServer) GenerateQRCode(context.Context, *GenerateQRCodeRequest) (*GenerateQRCodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateQRCode not implemented")
}
func (UnimplementedEquipmentServiceServer) mustEmbedUnimplementedEquipmentServiceServer() {}
func (UnimplementedEquipmentServiceServer) testEmbeddedByValue()                          {}

// UnsafeEquipmentServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to EquipmentServiceServer will
// result in compilation errors.
type UnsafeEquipmentServiceServer interface {
	mustEmbedUnimplementedEquipmentServiceServer()
}

func RegisterEquipmentServiceServer(s grpc.ServiceRegistrar, srv EquipmentServiceServer) {
	// If the following call pancis, it indicates UnimplementedEquipmentServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&EquipmentService_ServiceDesc, srv)
}

func _EquipmentService_CreateEquipment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateEquipmentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EquipmentServiceServer).CreateEquipment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EquipmentService_CreateEquipment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EquipmentServiceServer).CreateEquipment(ctx, req.(*CreateEquipmentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EquipmentService_GetEquipment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEquipmentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EquipmentServiceServer).GetEquipment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EquipmentService_GetEquipment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EquipmentServiceServer).GetEquipment(ctx, req.(*GetEquipmentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EquipmentService_ListEquipment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListEquipmentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EquipmentServiceServer).ListEquipment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EquipmentService_ListEquipment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EquipmentServiceServer).ListEquipment(ctx, req.(*ListEquipmentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EquipmentService_UpdateEquipment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateEquipmentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EquipmentServiceServer).UpdateEquipment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EquipmentService_UpdateEquipment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EquipmentServiceServer).UpdateEquipment(ctx, req.(*UpdateEquipmentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EquipmentService_DeleteEquipment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteEquipmentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EquipmentServiceServer).DeleteEquipment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EquipmentService_DeleteEquipment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EquipmentServiceServer).DeleteEquipment(ctx, req.(*DeleteEquipmentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EquipmentService_GetEquipmentHealth_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEquipmentHealthRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EquipmentServiceServer).GetEquipmentHealth(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EquipmentService_GetEquipmentHealth_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EquipmentServiceServer).GetEquipmentHealth(ctx, req.(*GetEquipmentHealthRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EquipmentService_UpdateEquipmentHealth_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateEquipmentHealthRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EquipmentServiceServer).UpdateEquipmentHealth(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EquipmentService_UpdateEquipmentHealth_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EquipmentServiceServer).UpdateEquipmentHealth(ctx, req.(*UpdateEquipmentHealthRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EquipmentService_ScheduleMaintenance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ScheduleMaintenanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EquipmentServiceServer).ScheduleMaintenance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EquipmentService_ScheduleMaintenance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EquipmentServiceServer).ScheduleMaintenance(ctx, req.(*ScheduleMaintenanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EquipmentService_GetMaintenanceHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMaintenanceHistoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EquipmentServiceServer).GetMaintenanceHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EquipmentService_GetMaintenanceHistory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EquipmentServiceServer).GetMaintenanceHistory(ctx, req.(*GetMaintenanceHistoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EquipmentService_GetEquipmentParts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEquipmentPartsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EquipmentServiceServer).GetEquipmentParts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EquipmentService_GetEquipmentParts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EquipmentServiceServer).GetEquipmentParts(ctx, req.(*GetEquipmentPartsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EquipmentService_UpdatePartStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePartStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EquipmentServiceServer).UpdatePartStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EquipmentService_UpdatePartStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EquipmentServiceServer).UpdatePartStatus(ctx, req.(*UpdatePartStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EquipmentService_GetEquipmentAnalytics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEquipmentAnalyticsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EquipmentServiceServer).GetEquipmentAnalytics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EquipmentService_GetEquipmentAnalytics_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EquipmentServiceServer).GetEquipmentAnalytics(ctx, req.(*GetEquipmentAnalyticsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EquipmentService_GetFleetOverview_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFleetOverviewRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EquipmentServiceServer).GetFleetOverview(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EquipmentService_GetFleetOverview_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EquipmentServiceServer).GetFleetOverview(ctx, req.(*GetFleetOverviewRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EquipmentService_GenerateQRCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateQRCodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EquipmentServiceServer).GenerateQRCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EquipmentService_GenerateQRCode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EquipmentServiceServer).GenerateQRCode(ctx, req.(*GenerateQRCodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// EquipmentService_ServiceDesc is the grpc.ServiceDesc for EquipmentService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var EquipmentService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.equipment.v1.EquipmentService",
	HandlerType: (*EquipmentServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateEquipment",
			Handler:    _EquipmentService_CreateEquipment_Handler,
		},
		{
			MethodName: "GetEquipment",
			Handler:    _EquipmentService_GetEquipment_Handler,
		},
		{
			MethodName: "ListEquipment",
			Handler:    _EquipmentService_ListEquipment_Handler,
		},
		{
			MethodName: "UpdateEquipment",
			Handler:    _EquipmentService_UpdateEquipment_Handler,
		},
		{
			MethodName: "DeleteEquipment",
			Handler:    _EquipmentService_DeleteEquipment_Handler,
		},
		{
			MethodName: "GetEquipmentHealth",
			Handler:    _EquipmentService_GetEquipmentHealth_Handler,
		},
		{
			MethodName: "UpdateEquipmentHealth",
			Handler:    _EquipmentService_UpdateEquipmentHealth_Handler,
		},
		{
			MethodName: "ScheduleMaintenance",
			Handler:    _EquipmentService_ScheduleMaintenance_Handler,
		},
		{
			MethodName: "GetMaintenanceHistory",
			Handler:    _EquipmentService_GetMaintenanceHistory_Handler,
		},
		{
			MethodName: "GetEquipmentParts",
			Handler:    _EquipmentService_GetEquipmentParts_Handler,
		},
		{
			MethodName: "UpdatePartStatus",
			Handler:    _EquipmentService_UpdatePartStatus_Handler,
		},
		{
			MethodName: "GetEquipmentAnalytics",
			Handler:    _EquipmentService_GetEquipmentAnalytics_Handler,
		},
		{
			MethodName: "GetFleetOverview",
			Handler:    _EquipmentService_GetFleetOverview_Handler,
		},
		{
			MethodName: "GenerateQRCode",
			Handler:    _EquipmentService_GenerateQRCode_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "equipment/v1/equipment.proto",
}
